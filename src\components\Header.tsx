import React, { memo } from 'react';

import '../styles/header.scss';
import { Link } from 'react-router-dom';
interface Props {
    title: string;
    active: string;
    children?: React.ReactNode;
}

const Header: React.FC<Props> = memo(props => {

    return (
        <div className='header'>
            <div className='header-center'>
                <div className='header-center-navigation'>
                    <div className='navigation'>
                        <div className={`navigation-item ${props.active === 'home' ? 'active' : ''}`}>
                            <Link>情况判断</Link>
                        </div>
                        <div className={`navigation-item ${props.active === 'page1' ? 'active' : ''}`}>
                            <Link>阶段划分</Link>
                        </div>
                        <div className={`navigation-item ${props.active === 'page2' ? 'active' : ''}`}>
                            <Link>方案生成</Link>
                        </div>
                        <div className={`navigation-item ${props.active === 'page3' ? 'active' : ''}`}>
                            <Link>方案评估</Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});
export default Header;
