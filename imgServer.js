import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const app = express();
const port = 3010;

// 获取当前文件的URL
const __filename = fileURLToPath(import.meta.url);
// 获取当前文件所在的目录
const __dirname = path.dirname(__filename);

// 指定本地图片存放的目录
const imagePath = path.join(__dirname, 'public', 'bmap-offline', 'tiles_satellite');

// 设置静态文件目录，以便访问本地图片
app.use('/tiles_satellite', express.static(imagePath));

// 设置路由，将图片文件的地址映射为URL
app.get('/image/:imageName', (req, res) => {
  const imageName = req.params.imageName;
  const imagePath = path.join(__dirname, 'images', imageName);

  // 发送图片文件
  res.sendFile(imagePath, (err) => {
    if (err) {
      res.status(404).send('File not found');
    }
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`Server is running at http://localhost:${port}`);
});
