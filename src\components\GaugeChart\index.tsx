import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

type GaugeChartProps = {
  width?: string | number;
  height?: string | number;
  dataUpdateInterval?: number;  // 数据更新间隔（毫秒）
  value?: number;  // 直接控制仪表盘值（可选）
  name?:string;               
};

const GaugeChart: React.FC<GaugeChartProps> = ({
  width = '100%',
  height = '250px',
  dataUpdateInterval = 2000,
  value,
  name = 'xx任务'
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const timer = useRef<number>();

  // 基础配置
  const gaugeData = [
    {
      value: value !== undefined ? Number(value.toFixed(2)) : 0,
      name: name,
      title: {
        offsetCenter: ['0%', '-120%'],
        color: '#ffffff',
        fontSize: 12,
        overflow: 'breakAll' as 'breakAll',
        width: 130
      },
      detail: {
        valueAnimation: true,
        offsetCenter: ['0%', '40%']
      }
    }
  ];

  // 初始化图表
  const initChart = () => {
    if (!chartRef.current) return;

    chartInstance.current = echarts.init(chartRef.current);

    const option: echarts.EChartsOption = {
      series: [
        {
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: { show: false },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 1,
              borderColor: '#464646'
            }
          },
          axisLine: { lineStyle: { width: 8 } },
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          data: gaugeData,
          title: { 
            fontSize: 12,
            width: 140,
            overflow: 'breakAll' as 'breakAll',
            padding: [0, 5]
          },
          detail: {
            fontSize: 14,
            color: '#ffffff',
            borderColor: 'inherit',
            formatter: '{value}%'
          },
          center: ['50%', '59%']
        }
      ],
      color: ['#3ba272'],
    };

    chartInstance.current.setOption(option);
  };

  // 更新数据逻辑
  const updateData = (newValue: number) => {
    if (!chartInstance.current) return;

    // 保留两位小数
    const formattedValue = Number(newValue.toFixed(2));
    gaugeData[0].value = formattedValue;
    
    chartInstance.current.setOption({
      series: [{ data: gaugeData }]
    });
  };

  // // 自动更新逻辑
  // const startAutoUpdate = () => {
  //   timer.current = setInterval(() => {
  //     const randomValue = +(Math.random() * 100).toFixed(2);
  //     updateData(randomValue);
  //   }, dataUpdateInterval);
  // };

  useEffect(() => {
    initChart();

    // // 如果未使用受控模式（value prop），则开启自动更新
    // if (value === undefined) {
    //   startAutoUpdate();
    // }

    return () => {
      // 清除定时器
      if (timer.current) clearInterval(timer.current);
      // 销毁图表实例
      if (chartInstance.current) chartInstance.current.dispose();
    };
  }, []);

  // 监听受控值变化
  useEffect(() => {
    if (value !== undefined) {
      updateData(value);
    }
  }, [value]);

  // 窗口大小变化时重新调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div
      ref={chartRef}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

export default GaugeChart;