# 后端流式输出缓冲配置说明

## 概述

修改后的后端流式接口支持智能缓冲策略，可以累积多个chunk后再发送，减少网络请求频率，提高传输效率。

## 配置参数

### 请求参数

在POST请求的JSON body中，可以添加 `buffer_config` 字段来配置缓冲策略：

```json
{
    "text": "您的作战筹划请求",
    "buffer_config": {
        "chunk_count": 5,        // 累积chunk数量（默认：5）
        "max_length": 100,       // 最大缓冲区长度（默认：100字符）
        "max_wait_time": 0.5     // 最大等待时间（默认：0.5秒）
    }
}
```

### 参数说明

1. **chunk_count** (默认: 5)
   - 累积多少个chunk后发送
   - 较大的值减少网络请求，但增加延迟
   - 建议范围：3-10

2. **max_length** (默认: 100)
   - 缓冲区最大字符数
   - 防止单个chunk过大导致延迟
   - 建议范围：50-200

3. **max_wait_time** (默认: 0.5)
   - 最大等待时间（秒）
   - 防止长时间无输出
   - 建议范围：0.2-1.0

## 智能缓冲策略

系统会在以下任一条件满足时发送数据：

1. **数量条件**: 累积的chunk数量达到 `chunk_count`
2. **大小条件**: 缓冲区字符数超过 `max_length`
3. **时间条件**: 距离上次发送超过 `max_wait_time`
4. **结构条件**: 检测到JSON结构字符（`{`, `}`, `[`, `]`, `"`）且缓冲区超过20字符

## 使用示例

### 默认配置（推荐）
```json
{
    "text": "生成作战方案"
}
```

### 快速响应配置
```json
{
    "text": "生成作战方案",
    "buffer_config": {
        "chunk_count": 3,
        "max_length": 50,
        "max_wait_time": 0.2
    }
}
```

### 高效传输配置
```json
{
    "text": "生成作战方案",
    "buffer_config": {
        "chunk_count": 8,
        "max_length": 150,
        "max_wait_time": 0.8
    }
}
```

## 响应格式

响应仍然是标准的Server-Sent Events (SSE)格式：

```
data: 累积的内容块

event: stats
data: 59.30

event: chunks
data: 1250

event: end
data: 
```

### 新增事件类型

- **stats**: 总耗时（秒）
- **chunks**: 总chunk数量
- **end**: 传输结束

## 性能优化建议

### 网络环境良好
```json
{
    "chunk_count": 6,
    "max_length": 120,
    "max_wait_time": 0.6
}
```

### 网络环境较差
```json
{
    "chunk_count": 3,
    "max_length": 60,
    "max_wait_time": 0.3
}
```

### 实时性要求高
```json
{
    "chunk_count": 2,
    "max_length": 40,
    "max_wait_time": 0.1
}
```

## 前端适配

前端代码无需修改，现有的流式处理逻辑完全兼容。缓冲后的数据块会被正确累积和解析。

## 监控和调试

- 查看 `event: chunks` 了解总chunk数量
- 查看 `event: stats` 了解总耗时
- 通过调整配置参数优化传输效率

## 注意事项

1. **平衡延迟和效率**: 较大的缓冲区减少网络请求但增加延迟
2. **JSON完整性**: 智能策略会优先保证JSON结构的完整性
3. **错误处理**: 缓冲过程中的错误会通过 `event: error` 返回
4. **兼容性**: 不提供 `buffer_config` 时使用默认配置，保持向后兼容
