import { atom } from 'recoil';

// 方案列表
export const schemeListAtom = atom<any>({
    key: 'schemeListAtom',
    default: [],
});

// 评估列表
export const assessListAtom = atom<any>({
    key: 'assessListAtom',
    default: [],
});

export const roundNumAtom = atom<string>({
    key: 'roundNumAtom',
    default: '1',
});

export const mapUrlAtom = atom<string>({
    key: 'mapUrlAtom',
    default: '',
});

export const taskDataAtom = atom<any>({
    key: 'taskDataAtom',
    default: {},
});

