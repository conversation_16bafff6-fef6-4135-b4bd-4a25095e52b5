.header {
    color: rgba(86, 186, 202, 1);
    width: 100%;
    height: 56px;
    line-height: 56px;
    background-color: rgba(0, 76, 122, 0.41);
    box-sizing: border-box;
    padding: 0 10px;
    display: flex;
    align-items: center;
    background-repeat: no-repeat;
    background-size: 1920px 56px;
    .company-logo {
        width: 389px;
    }
    .logo-sub {
        width: 300px;
    }
    .dropdown-icon {
        width: 23px;
        height: 20px;
    }
    .header-center {
        position: relative;
        top: 7px;
        margin-left: 50px;
        height: 56px;
        line-height: 56px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .header-center-navigation {
        display: flex;
        align-items: center;
        .navigation {
            display: flex;
            margin-left: 23px;
        }
    }
    .navigation-item {
        background-image: url(../assets/image/tab.png);
        background-repeat: no-repeat;
        background-size: contain;
        width: 170px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 16px;
        font-family: PuHuiTi35;
        font-weight: normal;
        font-style: italic;
        cursor: pointer;

        a {
            outline-style: none;
            text-decoration-line: none;
            color: rgba(115, 253, 255, 1);
        }
        &.active {
            background-image: url(../assets/image/active-tab.png);
            a {
                color: #ffffff;
            }
        }
    }
    .header-toolbar {
        display: flex;
        align-items: center;
        height: 56px;
        line-height: 56px;
    }
    .date-now,
    .date-time {
        line-height: 1;
    }
    .date-now {
        font-size: 12px;
        font-family: REEJI;
        font-weight: normal;
        color: #0de8fb;
    }
    .date-time {
        width: 120px;
        font-size: 28px;
        font-family: REEJI;
        font-weight: normal;
        color: #ffffff;
    }
    .date-week {
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        margin-top: 4px;
    }
    .header-title {
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
    }
}
.scene-list-men {
    width: 200px !important;
    background: rgba(37, 77, 115, 1);
    .ant-divider {
        margin: 7px 0;
        border-block-start: 1px solid rgba(5, 5, 5, 0.5);
    }
    .create-project-wrap {
        padding: 0 10px 3px 10px;
    }
    .ant-select-item {
        color: #fff !important;
    }
    .ant-select-item-option-selected {
        background: rgba(9, 220, 254, 0.49) !important;
        border-radius: 0;
    }
}
.project-select {
    font-size: 16px;
    font-weight: normal;
    color: #1ac9e3;
    line-height: 30px;

    .ant-select-selector {
        background: rgba(9, 220, 254, 0.5) !important;
        border-radius: 0 !important;
        border: 0 !important;
    }
}
.training-conf {
    &.create-project-modal {
        .ant-modal {
            width: 600px !important;
        }
        .ant-modal-content {
            height: auto;
        }
    }
}

@media screen and (min-width: 3840px) {
    .header {
        background-size: 3840px 112px;
        height: 112px;
        line-height: 112px;
        .header-center {
            margin-left: 260px;
            height: 112px;
            line-height: 112px;
        }
        .company-logo {
            width: 798px;
        }
        .logo-sub {
            width: 600px;
        }
        .navigation-item {
            width: 340px;
            height: 64px;
            line-height: 64px;
            font-size: 32px;
        }
        .header-toolbar {
            height: 112px;
            line-height: 112px;
        }
        .date-now {
            font-size: 24px;
        }
        .date-time {
            font-size: 56px;
        }
        .date-week {
            font-size: 24px;
            margin-top: 8px;
        }
        .header-title {
            font-size: 48px;
        }
        .home-model-list .model-handler {
            font-size: 32px;
            line-height: 40px;
            top: 8px;
        }
        .dropdown-icon {
            width: 46px;
            height: 40px;
            position: relative;
            top: 6px;
        }
    }
}
