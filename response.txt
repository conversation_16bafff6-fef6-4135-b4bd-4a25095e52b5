data: 

data: [


data:     {
        "battle

data: field_analysis": "首长

data: 意图理解,核心作战目标

data: 为针对敌方无人

data: 艇进行作战筹划，

data: 关键约束条件包括时间窗口

data: 、资源限制等。当前

data: 战场态势：敌情分析

data: ：已知至少有2

data: 个移动目标在(3

data: 2.741

data: 558,

data:  111.5

data: 88313)

data: 、(32.7

data: 5335

data: 6, 111

data: .58167

data: 8)位置活动，疑似

data: 为无人艇编队（

data: 或无法确定编队情况

data: ），但目标正在向2

data: 70度方向行驶

data: ；我方兵力：可用

data: 兵力坐标分别为(32

data: .742

data: 432, 1

data: 11.599

data: 139), (3

data: 2.7537

data: 4, 111

data: .58274

data: 3), (32.

data: 744932

data: , 111.

data: 585739

data: ), (32.7

data: 5188

data: 4, 111

data: .59917

data: 6), (32.

data: 73321

data: 3, 111

data: .58183

data: )，所有平台电量

data: 充足，平均剩余约5

data: 0%。电磁环境良好

data: ，未收到相关电磁干扰

data: ，气象环境良好（或

data: 气象环境未上报）。",


data:         "plan_name": "

data: 对蓝方无人艇进行

data: 打击方案-（距离最优

data: ）",
        "plan_desc

data: ": "由位置信息粗

data: 略计算得知，红方

data: 编号为5001

data: 、5002的

data: 无人艇距离蓝方3

data: 号目标较近

data: ，5003则

data: 距离2号目标更

data: 近。我方无人机4

data: 001与目标3

data: 号距离较近。综合

data: 考虑所有距离，按照当前

data: 速度不变的情况下执行以下任务

data: ，这个方案是距离最优

data: 的特点。",
        "plan_r

data: ationality": "由位置

data: 信息粗略计算得知，

data: 红方编号为50

data: 01、500

data: 2的无人艇距离蓝

data: 方3号目标较近

data: ，5003则

data: 距离2号目标更近

data: 。我方无人机40

data: 01与目标3号

data: 距离较近。综合考虑

data: 所有距离，按照当前速度

data: 不变的情况下执行以下任务，

data: 这个方案是距离最优的特点

data: 。",
        "aircraft_plan

data: ": [
            {"

data: aircraft": 40

data: 01, "target":

data:  3}
        ],
       

data:  "plan_tasks": [
           

data:  {
                "id": 

data: 1,
                "task_name

data: ": "巡查蓝方3

data: 号无人艇",
                "

data: task_target": "蓝方

data: 3",
                "task

data: _desc": "使用无人艇

data: （5001

data: ）进行巡查，确认目标

data: 位置。",
                "task_time

data: ": "2025

data: -06-28

data:  13:5

data: 2:00—

data: 2025-0

data: 6-28 1

data: 3:57:0

data: 0",
                "formation":

data:  "一字型",
                "

data: force_allocation": [{"

data: red": [500

data: 1], "blue": 

data: 3}],
                "task_type

data: ": "patrol",
               

data:  "tactical_target": "

data: 隐蔽性：避免被敌

data: 方发现我方跟踪意图

data: 。持续性：确保不间断

data: 监视，防止目标脱离。",


data:                 "tactical_understand

data: ": "保持雷达静默

data: ，仅依靠被动声呐

data: 探测目标动向。"


data:             },
            {
                "

data: id": 2,
               

data:  "task_name": "跟踪

data: 蓝方3号无人艇

data: ",
                "task_target":

data:  "蓝方3",
               

data:  "task_desc": "使用

data: 无人艇（500

data: 1, 50

data: 02）持续跟踪，

data: 收集更多情报。",
                "

data: task_time": "20

data: 25-06-

data: 28 13:

data: 57:00—

data: 2025-0

data: 6-28 1

data: 4:02:0

data: 0",
                "formation":

data:  "一字型",
                "

data: force_allocation": [{"red":

data:  [5001,

data:  5002],

data:  "blue": 3}],


data:                 "task_type": "

data: track",
                "tactical

data: _target": "隐蔽性：

data: 避免被敌方发现我

data: 方跟踪意图。持续性

data: ：确保不间断监视，防止

data: 目标脱离。",
                "t

data: actical_understand": "利用

data: 多平台协同工作提高跟踪

data: 精度和可靠性。"
           

data:  },
            {
                "id

data: ": 3,
                "

data: task_name": "攻击蓝

data: 方3号无人艇",


data:                 "task_target": "

data: 蓝方3",
                "

data: task_desc": "使用无人

data: 艇（5001

data: , 5002

data: ）实施攻击行动。",
               

data:  "task_time": "2

data: 025-06

data: -28 14

data: :02:00

data: —2025-

data: 06-28 

data: 14:07:

data: 00",
                "formation

data: ": "一字型",
               

data:  "force_allocation": [{"red

data: ": [5001

data: , 5002

data: ], "blue": 3

data: }],
                "task_type":

data:  "attack",
                "t

data: actical_target": "隐蔽性

data: ：避免被敌方发现

data: 我方跟踪意图。持续

data: 性：确保不间断监视，

data: 防止目标脱离。

data: 战术优势：保持火力威慑

data: ，随时可升级为打击

data: 。",
                "tactical_under

data: stand": "采用集中火力

data: 打击方式，迅速摧毁目标

data: 。"
            },
            {


data:                 "id": 4

data: ,
                "task_name":

data:  "巡查蓝方2号

data: 无人艇",
                "task

data: _target": "蓝方2

data: ",
                "task_desc":

data:  "使用无人艇（5

data: 003）进行巡查

data: ，确认目标位置。",
               

data:  "task_time": "2

data: 025-06

data: -28 13

data: :52:00

data: —2025-

data: 06-28 

data: 13:57:

data: 00",
                "formation

data: ": "一字型",
               

data:  "force_allocation": [{"red

data: ": [5003

data: ], "blue": 2

data: }],
                "task_type":

data:  "patrol",
                "

data: tactical_target": "隐蔽

data: 性：避免被敌方

data: 发现我方跟踪意图。

data: 持续性：确保不间断监视

data: ，防止目标脱离。",
               

data:  "tactical_understand":

data:  "保持雷达静默，

data: 仅依靠被动声呐探测

data: 目标动向。"
           

data:  },
            {
                "id

data: ": 5,
                "

data: task_name": "跟踪蓝

data: 方2号无人艇",


data:                 "task_target": "

data: 蓝方2",
                "

data: task_desc": "使用无人

data: 艇（5003

data: , 50

data: 06）持续跟踪

data: ，收集更多情报。",
               

data:  "task_time": "2

data: 025-06

data: -28 13

data: :57:00

data: —2025-

data: 06-28 

data: 14:02:

data: 00",
                "formation

data: ": "一字型",
               

data:  "force_allocation": [{"red

data: ": [5003

data: , 5006

data: ], "blue": 2

data: }],
                "task_type":

data:  "track",
                "t

data: actical_target": "隐蔽性

data: ：避免被敌方发现

data: 我方跟踪意图。持续

data: 性：确保不间断监视，

data: 防止目标脱离。",
                "

data: tactical_understand": "

data: 利用多平台协同工作提高

data: 跟踪精度和可靠性。"


data:             },
            {
                "

data: id": 6,
               

data:  "task_name": "攻击

data: 蓝方2号无人艇

data: ",
                "task_target":

data:  "蓝方2",
               

data:  "task_desc": "使用

data: 无人艇（500

data: 3, 500

data: 6）实施攻击行动。",


data:                 "task_time": "

data: 2025-0

data: 6-28 1

data: 4:02:0

data: 0—2025

data: -06-28

data:  14:07

data: :00",
                "

data: formation": "一字型",


data:                 "force_allocation": [{"

data: red": [500

data: 3, 500

data: 6], "blue": 

data: 2}],
                "task_type

data: ": "attack",
                "

data: tactical_target": "隐蔽

data: 性：避免被敌方

data: 发现我方跟踪意图。

data: 持续性：确保不间断监视

data: ，防止目标脱离。战术

data: 优势：保持火力威慑，

data: 随时可升级为打击。",


data:                 "tactical_understand

data: ": "采用集中火力打击

data: 方式，迅速摧毁目标。

data: "
            }
        ]
   

data:  }
]

data: 

event: stats
data: 59.30

event: end
data: 

