.map-container {
   position: relative;
}
.dom-Modal {
    position: absolute;
    right: 12px;
    top: 12px;
    width: 400px;
    // height: 92%;
    z-index: 999;
    background-image: url('../../assets/newImg/img1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-color: #000000;
 }
 
 .icon-d {
    width: 100%;
    text-align: right;
    padding: 32px;
 }
 
 .dom-Modal-icon {
    color: rgb(184, 20, 61);
    font-size: 20px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 8px;
 }
 
 .map-Modal-list {
    // height: 60vh;
    text-align: left;
    font-size: 2.2vh;
    color: #fff;
    overflow: auto;
 }
 
 .map-Modal-list::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 4px;
    scrollbar-arrow-color: black;
 
 }
 
 .map-Modal-list::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px #00E0DB;
    background: #00E0DB;
    scrollbar-arrow-color: black;
 }
 
 .map-Modal-list::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
 }
 
 .map-dom-list {
    width: 100%;
    /* height: 40px !important; */
    margin-bottom: 5px;
 }
 
 .dom-icon {
    width: 2%;
 }
 
 .dom-icon svg {
    fill: #00E0DB;
    width: 100%;
    height: 100%;
    vertical-align: middle;
 }

 .con-title {
    border: solid 1px #00E0DB;
    border-radius: 1vh;
    padding: 12px;
    color: #fff;
    font-size: 12px;
    background: rgba(141, 138, 138,0.3);
 }

 .con-title-h2 {
   text-align: center;
   font-size: 16px;
   color: #15fffd;
 }
 
 .con-list {
    font-size: 14px;
    margin: 16px 0;
 }