.chat-dom {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /*top: 6%;*/
    height: 100%;
    /*max-height: 100%;*/
    background-image: linear-gradient(180deg, rgba(15, 17, 21, 0.80) 0%, rgba(35, 41, 53, 0.50) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    /*border-left: none;*/
    /*border-top: none;*/
    /*border-bottom: none;*/
    border-radius: 4px;
}

.content {
    height: 100%;
    overflow-y: scroll;
    display: flex;
    flex-wrap: wrap;
    // flex-direction: column;
}

.table-dom {
    width: 100%;

    .ant-table-thead>tr>th {
        padding: 3px !important;
    }

    .ant-table-tbody>tr>td {
        padding: 3px !important;
    }

}

:where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper tr.ant-table-expanded-row >th, :where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper tr.ant-table-expanded-row:hover >th, :where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper tr.ant-table-expanded-row >td, :where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper tr.ant-table-expanded-row:hover >td {
    background: #051519 !important;
}

:where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper .ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin-block: -16px;
    margin-left: -8px;
}

:where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:first-child {
    border-start-start-radius: 0px;
}

:where(.css-dev-only-do-not-override-ed5zg0).ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:last-child {
    border-start-end-radius: 0px;
}