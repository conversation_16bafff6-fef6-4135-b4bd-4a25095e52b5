export const transRedskill = (data) => {
    const result = [];
    const skills = Object.keys(data.red_skill);
  
    skills.forEach((key) => {
      const skillName = data.red_skill[key];
      const skillNum = data.red_num[key];
      const red_loc = data.red_loc[key];
      result.push({ [key]:  skillNum + ' - ' + skillName + ' - 位置：' + red_loc});
    });
  
    return result.reduce((acc, obj) => {
        const skill = Object.keys(obj)[0];
        const num = obj[skill];
        return acc + `  ${skill}：${num} \n`;
      }, '');
  }

  export const transBlueskill = (data) => {
    const result = [];
    const skills = Object.keys(data.blue_skill);
  
    skills.forEach((key) => {
      const skillName = data.blue_skill[key];
      const skillNum = data.blue_num[key];
      const blue_loc = data.blue_loc[key];
      result.push({ [key]:  skillNum + ' - ' + skillName + ' - 位置：' + blue_loc});
    });
  
    return result.reduce((acc, obj) => {
        const skill = Object.keys(obj)[0];
        const num = obj[skill];
        return acc + `  ${skill}：${num} \n`;
      }, '');
  }

  export const deduplicateByLabel = (arr) => {
    const result = {};
    arr.forEach((item) => {
      result[item.label] = item;
    });
    return Object.values(result);
  }