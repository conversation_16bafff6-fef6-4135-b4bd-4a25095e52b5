import { useEffect, useState, useRef } from 'react';
import { CloseSquareFilled } from "@ant-design/icons";

import L from 'leaflet'; // 引入 Leaflet
import 'leaflet/dist/leaflet.css'; // 引入 Leaflet 样式
import './index.scss'

import { mapDataAtom } from '../../state';
import { useRecoilValue } from 'recoil';

import red from '../../assets/newImg/red.png'; //红飞机
import redC from '../../assets/newImg/r1-1.png'; //红船
import blue from '../../assets/newImg/blue.png'; //蓝飞机
import blueC from '../../assets/newImg/b1-1.png'; //蓝船
import p1 from '../../assets/newImg/p1.png'; //红方跑道
import p2 from '../../assets/newImg/p2.png'; //蓝方跑道

// 设置自定义图标
const customIcon = (iconUrl: any) => new L.Icon({
    iconUrl, // 标记图标
    iconSize: [20, 20], // 图标大小
    iconAnchor: [16, 32], // 锚点
  });

const OfflineMap = () => {
  const mapRef = useRef(null);
    const [initMap, setInitMap] = useState<any[]>([24.539685, 122.244043]);
    const mapData = useRecoilValue(mapDataAtom);
    const [infoData, setInfoData] = useState<any>({});
    const [clickShow, setClickShow] = useState<boolean>(false);

  
    useEffect(() => {
        // 创建地图实例
        const map = L.map('map').setView(initMap, 6); // 设置地图中心和缩放级别
        mapRef.current = map;
        // 使用瓦片层，设置本地瓦片路径
        L.tileLayer('offline-map/{z}/{x}/{y}.png', {
          maxZoom: 9,
          minZoom: 1,
        }).addTo(map);

        console.info(mapData, '-- mapData--');
        if(mapData) {
            const { area, route, red_force, blue_force } = mapData || {};
            const { Aircraft, Ship, Facility, Start_point, Goal_point } = red_force || {};
             // 画红方飞机
             Aircraft && Aircraft.forEach(point => {
              drawMarkers(point, 'red_air')
             });
            // 画红方船只
            Ship && Ship.forEach(point => {
                drawMarkers(point, 'red_ship')
             });
             // 画红方设施
             Facility && Facility.forEach(point => {
                drawMarkers(point, 'red_facility')
             });
             // 画红方起点
             if (Start_point) {
                 drawMarkers(Start_point, 'red_facility', '起点')
             }
             if (Goal_point) {      
                 // 画红方目标点
                 drawMarkers(Goal_point, 'red_facility', '目标点')
             }

             if (blue_force) {
                 // 画蓝方飞机
                 blue_force?.Aircraft.forEach(point => {
                    drawMarkers(point, 'blue_air')
                 });
                // 画蓝方船只
                 blue_force?.Ship.forEach(point => {
                    drawMarkers(point, 'blue_ship')
                 });
                 // 画蓝方设施
                 blue_force?.Facility.forEach(point => {
                        drawMarkers(point, 'blue_facility')
                 })
             }
             
             // 画区域
             if (area) {
                drawArea(area)
             }
             // 画航线
             if (route) {
              drawLines(route)
             }      
          }
        // 清理副作用（如卸载地图）
        return () => {
          map.remove();
        };
      }, [mapData]);

    // 绘制marker点
    const drawMarkers = (data: any, type?: string, title?: any) => {
      // 创建地图实例
      const map = mapRef.current;

      const ic =
      type === 'red_air'
          ? red
          : type === 'red_ship'
          ? redC
          : type === 'blue_air'
          ? blue
          : type === 'blue_ship'
          ? blueC
          : type === 'red_facility'
          ? p1
          : type === 'blue_facility'
          ? p2
          : '';

      const marker = L.marker([data.纬度, data.经度], { icon: customIcon(ic) }).addTo(map);
      title && marker.bindPopup(title);

      marker.addEventListener('click', function (e: any) {
                console.log(data, '--- data --');
                setInfoData(data || {});
                setClickShow(true);
                // openLeftModal(data);
            });
    };

     // 绘制线 (轨迹线)
     const drawLines = (data: any) => {
       const map = mapRef.current;
      const polyline = L.polyline(data, { color: 'red', weight: 2 }).addTo(map);
        // 地图平移到合适的视野
      map.fitBounds(polyline.getBounds());
  };

     // 绘制区域
     const drawArea = (data: any) => {
      const map = mapRef.current;
      const arr = data && data.length > 0 && data.map((item: any) => {
          const point = [item[1], item[0]];
          return point;
      });
      const polygon = L.polygon(arr, { color: 'yellow', fillColor: 'yellow', fillOpacity: 0.1, weight: 1}).addTo(map);
      polygon.bindPopup('打击区域');
      
  };

  return (
    <div className='map-container'>
    {clickShow ? (
      <div className="dom-Modal">
        <div className="icon-d">
          <CloseSquareFilled className="dom-Modal-icon" onClick={() => { setClickShow(false) }} />
          <div className="map-Modal-list">
              {infoData ? (
                <div className="con-title">
                  <h2 className='con-title-h2'>{infoData['名称']}</h2>
                  {Object.keys(infoData).map((i: any, x: number) => {
                    return (
                      <div className="con-list" key={x}>
                        <span>{i}</span>：<span>{infoData[i]}</span>
                      </div>
                    );
                  })}
                </div>
              ) : ''}
          </div>
        </div>
      </div>
    ) : ''}

    <div
      id="map"
      style={{ width: '100%', height: '100vh' }}
    >
      {/* 地图容器 */}
    </div>
    </div>
  );
};

export default OfflineMap;
