import { defineConfig, UserConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path'

// const ipAddress = 'http://**********:8000/'
const ipAddress = 'http://0.0.0.0:8000/'
// const ipAddress = 'http://***********:8000/'
// const ipAddress = 'http://***************:8000/'

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    server: {
        proxy: {
            '/701api': {
                target: ipAddress,
                changeOrigin: true,
                rewrite(path) {
                    return path.replace(/^\/701api/, '');
                },
            },
        },
    },
    // build: {
    //     rollupOptions: {
    //       input: {
    //         // 在这里手动指定需要打包的文件，不管它们是否被项目代码引用
    //         main: path.resolve(__dirname, 'index.html'), // 主入口
    //         map: path.resolve(__dirname, 'src/components/Bmap/lib/map3.0.js'), // 需要打包的外部文件
    //         // other: path.resolve(__dirname, 'src/assets/other-file.js'), // 其他想打包的文件
    //       },
    //       output: {
    //         // 配置输出文件名规则
    //         entryFileNames: `[name].[hash].js`,
    //         chunkFileNames: `[name].[hash].js`,
    //         assetFileNames: `[name].[hash].[ext]`,
    //       },
    //     },
    //   },
} satisfies UserConfig);