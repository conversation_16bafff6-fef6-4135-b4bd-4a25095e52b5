.chat-dom {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /*top: 6%;*/
    height: 100%;
    /*max-height: 100%;*/
    background-image: linear-gradient(180deg, rgba(15, 17, 21, 0.80) 0%, rgba(35, 41, 53, 0.50) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    /*border-left: none;*/
    /*border-top: none;*/
    /*border-bottom: none;*/
    border-radius: 4px;
}

.chat-content {
    /* position: relative; */
    overflow-y: scroll;
    margin-left: auto;
    margin-right: auto;
    width: 98%;
    height: calc(100% - 80px);

    /* 优化滚动条样式 */
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(64, 150, 255, 0.6);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: rgba(64, 150, 255, 0.8);
    }
}

.chat-item p {
    margin: 0;
    padding: 4px;
}

.chat-item-2 .chat-receiver {
    justify-content: end;

}

.chat-receiver {
    display: flex;
    align-items: flex-start;
    padding-top: 10px
}

.chat-sender {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    padding-top: 10px
}

.sender-content {
    width: fit-content;
    padding: 12px 12px;
    font-size: 14px;
    white-space: break-spaces;
    text-overflow: ellipsis;
    color: #BACAE0;
    font-weight: 400;
    background: #027B5C;
    border-radius: 8px 8px 0 8px;
    line-height: 20px;
    word-break: normal
}

.receiver-content {
    // min-width: 200px;
    max-height: none; // 移除高度限制，让内容完全显示
    overflow-y: visible; // 改为可见，不需要滚动
    white-space: pre-wrap;
    max-width: 70%;
    font-size: 14px;
    text-overflow: ellipsis;
    font-weight: 400;
    border-radius: 8px 8px 8px 0;
    line-height: 20px;
}

.receiver-content>div {
    margin: 8px 20px;
    cursor: pointer;
    color: #ffffff;
    text-decoration: solid;
}

.receiver-content>div:hover {
    opacity: 0.8;
}

.receiver-content p {
    color: rgb(179, 39, 29);
}

.receiver-content .chat-info {
    color: #BACAE0 !important;
}


textarea.ant-input {
    border: none;
    background-color: transparent;
    outline: none;
    /*width: 95%;*/
    box-shadow: none;
}

textarea.ant-input:focus {
    border: none;
    box-shadow: none;
}

.bottom_right {
    margin-left: 12px;
}

.send {
    width: 100px;
    height: 30px;
    background: #027B5C;
    border: 1px solid rgba(0, 223, 133, 0.3);
    font-size: 14px;
    color: #FFFFFF;
    letter-spacing: 0;
    font-weight: 500;
    /*float: right;*/
}

.bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-radius: 4px;
    line-height: 10px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
    padding: 0 20px;

    .audio-icon {
        width: 28px;
        // height: 26px;
        line-height: 26px;
        margin-left: 12px;
        color: #4096ff;
        cursor: pointer;
    }

    .pause {
        color: yellow;
    }
}

.avatar {
    padding: 10px;
    border-radius: 30%;
    font-size: 12px;
}

.helper-content {
    margin-left: 6px;
    border: 1px solid rgb(64, 150, 255);
    background: rgb(64, 150, 255, 0.5);

    >p {
        color: #FFFFFF
    }
}

.user-content {
    margin-right: 6px;
    border: 1px solid rgb(250, 173, 20);
    background: rgb(250, 173, 20, 0.5);
}

.tooltip-click {
    color: #FFFFFF !important;
    cursor: pointer;
    text-decoration: underline;
}

.tooltip-click:hover {
    color: rgb(64, 150, 255) !important;
}

.task-content {
    margin: 0 !important;

    p {
        color: #FFFFFF;
    }
}

.all-spinning {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(20, 19, 19, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-item {
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    .input-label {
        display: inline-block;
        width: 130px;
        margin-right: 8px;
        text-align: right;
    }

    input {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: rgba(0, 0, 0, 0.88); // 新增文字颜色
        background-color: #ffffff; // 确保背景为白色

        &::placeholder {
            color: #bfbfbf !important; // 强制placeholder颜色
          }

        &:focus {
            &::placeholder {
                color: transparent;
            }
        }
    }
}

.form-buttons {
    margin-top: 16px;
    text-align: center;

    button {
        color: #000;
        margin: 0 8px;
        // padding: 4px 16px;
    }
}

// 临机决策样式
.decision-content {
  width: 100%;
  
  .decision-display {
    // background-color: #f5f5f5;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    
    h3 {
      margin-top: 0;
      color: #1677ff;
      text-align: center;
    }
    
    .decision-list {
      list-style: none;
      padding: 0;
      
      .decision-item {
        // background-color: #fff;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        
        .decision-item-header {
          font-weight: bold;
          color: #1677ff;
          margin-bottom: 5px;
        }
        
        .decision-item-content {
          color: #333;
          margin-bottom: 10px;
        }
        
        // 子项列表样式
        .decision-sub-list {
          list-style: none;
          padding-left: 15px;
          margin-top: 8px;
          
          .decision-sub-item {
            // background-color: #f9f9f9;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 8px;
            
            .decision-sub-item-header {
              font-weight: bold;
              color: #1677ff;
              font-size: 0.9em;
              margin-bottom: 3px;
            }
            
            .decision-sub-item-content {
              font-size: 0.9em;
            }
          }
        }
        
        // 任务列表样式
        .decision-tasks {
          margin-top: 10px;
          border-top: 1px dashed #d9d9d9;
          padding-top: 8px;
          
          h4 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #1677ff;
            font-size: 0.95em;
          }
          
          ul {
            list-style: none;
            padding-left: 0;
            
            .decision-task {
              display: flex;
              margin-bottom: 5px;
              
              .decision-task-name {
                font-weight: bold;
                margin-right: 8px;
                min-width: 80px;
              }
              
              .decision-task-desc {
                flex: 1;
                color: #666;
              }
            }
          }
        }
      }
    }
    
    .decision-properties {
      .decision-property {
        display: flex;
        margin-bottom: 5px;
        
        .decision-property-name {
          font-weight: bold;
          margin-right: 5px;
          min-width: 100px;
        }
        
        .decision-property-value {
          flex: 1;
          word-break: break-all;
        }
      }
    }
    
    .decision-format-tip {
      margin-top: 15px;
      padding: 10px;
      // background-color: #fffbe6;
      border: 1px solid #ffe58f;
      border-radius: 4px;
      
      p {
        color: #d48806;
        margin-top: 0;
        margin-bottom: 8px;
        font-weight: bold;
      }
      
      pre {
        // background-color: #fff;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        margin: 0;
        color: #333;
        font-size: 0.9em;
        overflow-x: auto;
      }
    }
  }
  
  .decision-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    margin-top: 15px;
    margin-left: 10px;
    
    button {
      background-color: #1677ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      min-width: 80px;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
      
      &:hover {
        background-color: #4096ff;
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(1px);
      }
      
      &.highlight-button {
        background-color: #52c41a;
        animation: pulse 1.5s infinite;
        
        &:hover {
          background-color: #73d13d;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.decision-edit-container {
  width: 100%;
  
  .decision-edit-header {
    margin-bottom: 10px;
    
    h3 {
      color: #1677ff;
      margin: 0 0 10px 0;
      text-align: center;
      font-size: 16px;
    }
  }
  
  .decision-edit-textarea {
    width: 100%;
    min-height: 200px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    font-family: monospace;
    resize: vertical;
    color: #333;
    // background-color: #fff;
  }
  
  .decision-edit-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    margin-top: 15px;
    margin-left: 10px;
    
    button {
      background-color: #1677ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      min-width: 80px;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
      
      &:hover {
        background-color: #4096ff;
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(1px);
      }
      
      &:nth-child(2) {
        background-color: #ff4d4f;
        
        &:hover {
          background-color: #ff7875;
        }
      }
    }
  }
}

// 确保编辑模式和查看模式宽度一致
.receiver-content {
  .decision-content, 
  .decision-edit-container {
    max-width: 100%;
    width: 100%;
  }
}