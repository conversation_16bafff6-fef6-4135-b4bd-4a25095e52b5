{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --host --port 3002", "build": "vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "express": "^4.21.0", "jsonrepair": "^3.12.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.11.1", "react-use": "^17.4.0", "recoil": "^0.7.7"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/lodash": "^4.14.195", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "antd": "^5.4.7", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "sass": "^1.62.1", "typescript": "^5.0.2", "vite": "^4.3.2"}}