import { FC, useCallback, useEffect, useRef, useState } from 'react';
import './index.scss';
import { Input, Button, Spin, message, Tooltip, Select } from 'antd';
import { PlayCircleOutlined, AudioFilled, PauseCircleFilled } from '@ant-design/icons';

import { api } from '../../api';
import { taskDataAtom } from '../../state';
import { useRecoilState } from 'recoil';

// 添加在import之后组件外部
function encodeWAV(samples: Float32Array, sampleRate: number): Blob {
    const buffer = new ArrayBuffer(44 + samples.length * 2);
    const view = new DataView(buffer);

    /* RIFF标识符 */
    writeString(view, 0, 'RIFF');
    /* RIFF长度 */
    view.setUint32(4, 36 + samples.length * 2, true);
    /* WAV文件类型 */
    writeString(view, 8, 'WAVE');
    /* 格式块标识符 */
    writeString(view, 12, 'fmt ');
    /* 格式块长度 */
    view.setUint32(16, 16, true);
    /* 音频格式（PCM） */
    view.setUint16(20, 1, true);
    /* 声道数 */
    view.setUint16(22, 1, true);
    /* 采样率 */
    view.setUint32(24, sampleRate, true);
    /* 字节率 */
    view.setUint32(28, sampleRate * 2, true); // 16-bit mono
    /* 块对齐 */
    view.setUint16(32, 2, true);
    /* 位深度 */
    view.setUint16(34, 16, true);
    /* 数据块标识符 */
    writeString(view, 36, 'data');
    /* 数据长度 */
    view.setUint32(40, samples.length * 2, true);

    // 写入PCM数据
    floatTo16BitPCM(view, 44, samples);

    return new Blob([view], { type: 'audio/wav' });
}

function floatTo16BitPCM(view: DataView, offset: number, input: Float32Array) {
    for (let i = 0; i < input.length; i++, offset += 2) {
        const s = Math.max(-1, Math.min(1, input[i]));
        view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
    }
}

function writeString(view: DataView, offset: number, string: string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

interface IProps {
    sendChatMsg?: (data: any) => void;
}

const colorObj: any = {
    success: '#FFFFFF',
    waiting: 'yellow',
    error: 'red',
    text: '#FFFFFF',
};

declare global {
    interface Window {
        webkitAudioContext: any;
    }
}

// interface IMessage {
//     msgId: number;
//     sendId: string;
//     content: any;
//     displayContent?: string; // 新增独立的内容状态
//     chatType: string;
//     // hasButton: boolean;
// }


const Chat: FC<IProps> = () => {
    // const [mapUrl, setMapUrl] = useRecoilState(mapUrlAtom);
    // const [taskData, setTaskData] = useRecoilState(taskDataAtom);
    const [resSuccess, setResSuccess] = useState(false);
    const [initData, setInitData] = useState<any>();
    // 编辑状态
    const [isEditing, setIsEditing] = useState(false);
    // 临时存储编辑数据
    const [tempData, setTempData] = useState<any>();
    // sendId 1-助手；2-用户
    const [messageList, setMessageList] = useState<any[]>([
        {
            msgId: Date.now() + Math.random() * 10000,
            sendId: '1',
            content: '',
            displayContent: '',
            chatType: 'toolptip',
            // hasButton: false,
        },
    ]);

    const [content, setContent] = useState<string>('');
    const messagesEnd = useRef<HTMLDivElement>(null);

    const [spinning, setSpinning] = useState(false);

    // const [streamContent, setStreamContent] = useState('');
    // const [isStreaming, setIsStreaming] = useState(false);
    // const [planData, setPlanData] = useState();
    // const [displayContent, setDisplayContent] = useState('');
    const streamIntervalRef = useRef<any>(null);
    const responseDataRef = useRef<any>([]);

    const scrollToBottom = () => {
        setTimeout(() => {
            const chatBox: any = document.getElementById('chatItems');
            chatBox.scrollTop = chatBox.scrollHeight - chatBox.clientHeight;
        }, 100);
    };

    // 进入编辑模式
    const handleEdit = (value: any) => {
        setTempData(value); // 初始化临时数据
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
    };

    // 保存修改
    const handleSave = () => {
        const transformed = {
            control_center: {
                lat: parseFloat(tempData.lat),     // 保持浮点型转换
                lon: parseFloat(tempData.lon)      // 保证地理坐标精度
            },
            // 直接使用原始值，保留数据类型
            strike_zone_radius: tempData.strike_zone_radius,
            surveillance_zone_radius: tempData.surveillance_zone_radius,
            tracking_zone_radius: tempData.tracking_zone_radius
        };
        // console.log(transformed);
        editInitData(transformed); // 调用保存函数
        setIsEditing(false);
        getInitData();
    };

    // 处理输入框修改
    const handleChange = (key: any, value: any) => {
        setTempData((prev: any) => ({
            ...prev,
            [key]: value
        }));
    };

    const editInitData = async (value: any) => {
        await api
            .editInitData(value)
            .then(res => {
                // console.log(res);
                setResSuccess(!resSuccess);
            })
            .catch(err => {
                console.log(err);
            })
    }
    const getInitData = async () => {
        await api
            .getInitData()
            .then(res => {
                setInitData(res.data)
                setTempData(res.data)
            })
            .catch(err => {
                console.log(err);
            })
    }
    // 每次接口返回数据要重新渲染对话页面
    useEffect(() => {
        getInitData()
        setMessageList(messageList);
    }, [resSuccess]);



    const simulateStreaming = (msgId: number, responseData: any[]) => {
        responseDataRef.current = responseData; // 确保数据引用更新
        let fullContent = ``;

        // 添加战场分析（只显示第一个元素的）
        if (responseData[0]?.battlefield_analysis) {
            fullContent += `<p style="font-weight: bold; color: #4096ff;text-align: center;">战场分析</p>`;
            fullContent += `<p>${responseData[0].battlefield_analysis}</p>`;
        }

        // 遍历所有方案
        responseData.forEach((plan, index) => {
            fullContent += `<p style="font-weight: bold; color: #4096ff; margin-top: 16px;text-align: center;" data-plan-index="${index}" class="plan-header"}>方案 ${index + 1}</p>`;

            // 方案基本信息
            fullContent += `<div>`;
            fullContent += `<p style="text-align: center;">${plan.plan_name}</p>`;
            fullContent += `<p style="text-align: center;"><strong>合理性描述</strong></p>`;
            fullContent += `<p>${plan.plan_desc}</p>`;
            fullContent += `<p>${plan.plan_rationality}</p>`;

            // 任务列表
            if (plan.plan_tasks?.length) {
                fullContent += `<p style="font-weight: bold; text-align: center;">详细任务</p>`;
                fullContent += `<ul style="padding-left: 0;">`;
                plan.plan_tasks.forEach((task: any) => {
                    fullContent += `<li style="list-style:none;">
                        <details class="task-details">
                            <summary class="task-summary" style="cursor: pointer; display: flex; align-items: center;">
                                <span class="arrow" style="display: inline-block; transition: transform 0.2s; margin-right: 8px;"></span>
                                <strong>${task.task_name}</strong>
                            </summary>
                            <div>
                                <p><em>时间：</em>${task.task_time}</p>
                                <p><em>描述：</em>${task.task_desc}</p>
                                <p><em>编队：</em>${task.formation}</p>
                                ${task.force_allocation?.length ?
                            `<p>${task.force_allocation.map((f: any) =>
                                `派遣我方兵力编号:${f.red?.join(',')}，敌方目标编号:${f.blue}`
                            ).join('<br>')}</p>`
                            : ''}
                            </div>
                        </details>
                    </li>`;
                });
                fullContent += `</ul>`;
            }
            fullContent += `</div>`;
            fullContent += `<style>
            .task-summary::-webkit-details-marker { display: none; }
            .task-details .arrow::before { content: '▶'; display: inline-block; }
            .task-details[open] .arrow::before { content: '▼'; }
            </style>`;
        });

        // 后续的流式输出逻辑保持不变...
        let currentIndex = 0;

        // 清除之前的定时器
        if (streamIntervalRef.current) {
            clearInterval(streamIntervalRef.current);
        }

        // 设置新的定时器
        streamIntervalRef.current = setInterval(() => {
            setMessageList(prev => prev.map(msg => {
                if (msg.msgId === msgId) {
                    return currentIndex <= fullContent.length ? {
                        ...msg,
                        displayContent: fullContent.slice(0, currentIndex++)
                    } : msg;
                }
                return msg;
            }));

            if (currentIndex > fullContent.length) {
                clearInterval(streamIntervalRef.current!);
                //   setIsStreaming(false);
            }
        }, 10);
    };

    // 在组件中通过useEffect添加事件监听
    useEffect(() => {
        const sendPlanData = async (params: any) => {
            await api
                .sendPlanData(params)
                .then(res => {
                    console.log(res);
                })
                .catch(err => {
                    console.log(err);
                })
        };

        const handlePlanClick = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (target.classList.contains('plan-header')) {
                const planIndex = target.dataset.planIndex;
                if (planIndex) {
                    // 从最新数据中获取对应方案
                    const selectedPlan = responseDataRef.current[Number(planIndex)];
                    sendPlanData(selectedPlan);
                    // console.log('点击的方案数据:', selectedPlan,planIndex);
                }
            }
        };

        document.addEventListener('click', handlePlanClick);
        return () => document.removeEventListener('click', handlePlanClick);
    }, []);

    // 在组件卸载时清理定时器
    useEffect(() => {
        return () => {
            if (streamIntervalRef.current) {
                clearInterval(streamIntervalRef.current);
            }
        };
    }, []);

    const send = async () => {
        if (content) {
            const msList = [...messageList];

            // 用户消息
            msList.push({
                msgId: Math.random() * 10000,
                sendId: '2',
                content: content,
                chatType: 'text',
            });

            // 助手消息
            const assistantMsgId = Date.now() + Math.random();
            msList.push({
                msgId: assistantMsgId,
                sendId: '1',
                content: '思考中...',
                chatType: 'text',
                isStreaming: true,
                displayContent: '思考中...'
            });

            setMessageList(msList);
            setContent('');
            // setIsStreaming(true);
            scrollToBottom();

            try {
                const res = await api.postVoice({
                    model_type: modelType,
                    text: content || '',
                });

                // setTaskData(res || {});

                // 开始模拟流式输出
                const updatedList = msList.map(msg => {
                    if (msg.msgId === assistantMsgId) {
                        return {
                            ...msg,
                            content: res,
                            isQuestData: true,
                            isStreaming: true,
                            displayContent: ''
                        };
                    }
                    return msg;
                });

                setMessageList(updatedList);

                // 开始模拟流式输出
                if (res) {
                    // setPlanData(res);
                    // console.log(res);
                    simulateStreaming(assistantMsgId, res); // 传入消息ID和响应数据;
                }

            } catch (err) {
                const updatedList = msList.map(msg => {
                    if (msg.msgId === assistantMsgId) {
                        return {
                            ...msg,
                            content: '执行失败！',
                            chatType: 'error',
                            isStreaming: false
                        };
                    }
                    return msg;
                });

                setMessageList(updatedList);
                // setIsStreaming(false);
            }

            scrollToBottom();
        }
    };

    const [modelType, setModelType] = useState('Qwen-72b');

    const [audioStatus, setAudioStatus] = useState(false);
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioChunksRef = useRef<Blob[]>([]);
    const audioStreamRef = useRef<MediaStream | null>(null);

    // const audioContextRef = useRef<AudioContext | null>(null);
    // const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
    // const processorNodeRef = useRef<ScriptProcessorNode | null>(null);
    // const audioDataRef = useRef<Float32Array[]>([]);

    // const startRecording = async () => {
    //     try {
    //         const stream = await navigator.mediaDevices.getUserMedia({
    //             audio: {
    //                 sampleRate: 16000,
    //                 channelCount: 1,
    //                 noiseSuppression: true,
    //                 echoCancellation: true
    //             }
    //         });

    //         // 创建音频上下文
    //         const audioContext = new AudioContext({ sampleRate: 16000 });
    //         audioContextRef.current = audioContext;

    //         // 创建音频源节点
    //         const sourceNode = audioContext.createMediaStreamSource(stream);
    //         sourceNodeRef.current = sourceNode;

    //         // 创建处理器节点
    //         const processorNode = audioContext.createScriptProcessor(4096, 1, 1);
    //         processorNodeRef.current = processorNode;

    //         // 收集原始PCM数据
    //         processorNode.onaudioprocess = (e) => {
    //             const channelData = e.inputBuffer.getChannelData(0);
    //             audioDataRef.current.push(new Float32Array(channelData));
    //         };

    //         // 连接节点
    //         sourceNode.connect(processorNode);
    //         processorNode.connect(audioContext.destination);
    //         setAudioStatus(true);
    //     } catch (error) {
    //         console.error('启动录音失败:', error);
    //         message.error('无法访问麦克风，请检查权限设置');
    //     }
    // };
    // const stopRecording = async () => {
    //     if (audioContextRef.current) {
    //         // 保证最后512个样本的处理
    //         await new Promise(resolve =>
    //             setTimeout(resolve, 100));

    //         // 合并数据时保持原始顺序
    //         const mergedData = mergeFloat32Arrays(audioDataRef.current);

    //         // 强制使用16kHz采样率
    //         const wavBlob = encodeWAV(mergedData, 16000);

    //         // 验证文件头有效性
    //         const header = await validateWAVHeader(wavBlob);
    //         console.log('WAV文件头验证:', header);

    //         // 创建下载链接
    //         // const url = URL.createObjectURL(wavBlob);
    //         // const a = document.createElement('a');
    //         // a.href = url;
    //         // a.download = 'recording.wav';
    //         // document.body.appendChild(a);
    //         // a.click();
    //         // URL.revokeObjectURL(url);
    //         // document.body.removeChild(a);

    //         // 发送到后端
    //         const formData = new FormData();
    //         formData.append('file', wavBlob, 'speech.wav'); // 字段名改为audio

    //         try {
    //             const response = await api.sendVoice(formData);
    //             setContent(prev => prev + (response?.text || ''));
    //         } catch (error) {
    //             console.error('语音识别失败:', error);
    //             message.error('无法处理音频，请检查格式');
    //         }
    //         setAudioStatus(false);
    //     }
    // };
    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,    // 强制指定采样率
                    channelCount: 1,
                    noiseSuppression: true,
                    echoCancellation: true
                }
            });

            // 重置旧数据（防止数据残留）
            audioChunksRef.current = [];
            audioStreamRef.current = stream;

            // 创建新的MediaRecorder实例（避免旧实例干扰）
            const mediaRecorder = new MediaRecorder(stream);
            mediaRecorderRef.current = mediaRecorder;

            // 数据收集回调（优化内存管理）
            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    // 限制数据块数量（防止内存溢出）
                    if (audioChunksRef.current.length < 30) {
                        audioChunksRef.current.push(e.data);
                    }
                }
            };

            mediaRecorder.start(200); // 设置数据收集间隔为200ms
            setAudioStatus(true);
        } catch (error) {
            console.error('启动录音失败:', error);
            message.error('无法启动录音，请检查麦克风权限');
        }
    };
    const stopRecording = async () => {
        try {
            // 确保正确停止MediaRecorder
            if (mediaRecorderRef.current?.state === 'recording') {
                // 创建停止Promise
                const stopPromise = new Promise<void>((resolve) => {
                    if (mediaRecorderRef.current) {
                        mediaRecorderRef.current.onstop = () => resolve();
                    }
                });

                // 先停止录音再处理数据
                mediaRecorderRef.current.stop();
                await stopPromise;

                // 立即停止所有音轨（关键修复点）
                audioStreamRef.current?.getTracks().forEach(track => {
                    track.stop();  // 强制停止音轨
                    track.enabled = false; // 禁用轨道
                });
                audioStreamRef.current = null;

                // 处理音频数据（保持原有逻辑）
                const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
                const arrayBuffer = await audioBlob.arrayBuffer();
                const audioContext = new AudioContext();

                // 增加音频上下文安全检测
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                }

                const decodedData = await audioContext.decodeAudioData(arrayBuffer);
                const merged = decodedData.getChannelData(0);

                // 强制指定16kHz采样率（解决采样率不匹配问题）
                const wavBlob = encodeWAV(merged, 16000);

                // 验证文件头
                const header = await validateWAVHeader(wavBlob);
                console.log('WAV文件头验证:', header);

                // 创建下载链接
                const url = URL.createObjectURL(wavBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'recording.wav';
                document.body.appendChild(a);
                a.click();
                URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 发送到后端（字段名改为后端要求的）
                const formData = new FormData();
                formData.append('file', wavBlob, 'speech.wav');

                const response = await api.sendVoice(formData);
                setContent(prev => prev + (response?.text || ''));
            }
        } catch (error) {
            console.error('语音识别失败:', error);
            message.error('语音识别失败');
        } finally {
            // 确保状态重置（关键修复点）
            mediaRecorderRef.current = null;
            audioChunksRef.current = [];
            setAudioStatus(false);
        }
    };


    // 辅助函数：合并Float32数组
    // const mergeFloat32Arrays = (arrays: Float32Array[]) => {
    //     const totalLength = arrays.reduce((acc, arr) => acc + arr.length, 0);
    //     const result = new Float32Array(totalLength);
    //     let offset = 0;
    //     arrays.forEach(arr => {
    //         result.set(arr, offset);
    //         offset += arr.length;
    //     });
    //     return result;
    // }

    // 辅助函数：验证WAV文件头
    const validateWAVHeader = async (blob: Blob) => {
        const header = await blob.slice(0, 44).arrayBuffer();
        const view = new DataView(header);
        return {
            riff: String.fromCharCode(...new Uint8Array(view.buffer, 0, 4)),
            fileSize: view.getUint32(4, true),
            wave: String.fromCharCode(...new Uint8Array(view.buffer, 8, 4)),
            fmt: String.fromCharCode(...new Uint8Array(view.buffer, 12, 4)),
            subchunkSize: view.getUint32(16, true),
            audioFormat: view.getUint16(20, true),
            channels: view.getUint16(22, true),
            sampleRate: view.getUint32(24, true),
            byteRate: view.getUint32(28, true),
            blockAlign: view.getUint16(32, true),
            bitsPerSample: view.getUint16(34, true),
            dataHeader: String.fromCharCode(...new Uint8Array(view.buffer, 36, 4)),
            dataSize: view.getUint32(40, true)
        };
    }
    const onAudioPlay = useCallback(async () => {
        if (!audioStatus) {
            await startRecording();
        } else {
            await stopRecording();
        }
    }, [audioStatus]);

    return (
        <div className='chat-dom'>
            <div id='chatItems' className='chat-content' ref={messagesEnd}>
                {messageList.map((item, index) => {
                    return (
                        <div className={`chat-item chat-item-${item.sendId}`} key={item.msgId || index}>
                            <div className='chat-receiver'>
                                {item?.sendId && item?.sendId === '1' ? (
                                    <>
                                        <div className='avatar' style={{ backgroundColor: '#4096ff' }}>
                                            助手
                                        </div>
                                        {item.chatType === 'toolptip' ? (
                                            <div className='receiver-content helper-content'>
                                                <p>您好，我是军事方案生成助手。</p>
                                                <>
                                                    {isEditing ? (
                                                        <>
                                                            <input
                                                                placeholder='请输入指挥中心经度'
                                                                value={tempData?.control_center?.lat}
                                                                onChange={(e) => handleChange('lat', e.target.value)}
                                                            />
                                                            <input
                                                                placeholder='请输入指挥中心纬度'
                                                                value={tempData?.control_center?.lon}
                                                                onChange={(e) => handleChange('lon', e.target.value)}
                                                            />
                                                            <input
                                                                placeholder='请输入打击区域半径'
                                                                value={tempData?.strike_zone_radius}
                                                                onChange={(e) => handleChange('strike_zone_radius', e.target.value)}
                                                            />
                                                            <input
                                                                placeholder='请输入跟踪区域半径'
                                                                value={tempData?.tracking_zone_radius}
                                                                onChange={(e) => handleChange('tracking_zone_radius', e.target.value)}
                                                            />
                                                            <input
                                                                placeholder='请输入监控区域半径'
                                                                value={tempData?.surveillance_zone_radius}
                                                                onChange={(e) => handleChange('surveillance_zone_radius', e.target.value)}
                                                            />
                                                            <div>
                                                                <button onClick={handleSave}>保存</button>
                                                                <button onClick={handleCancel}>取消</button>
                                                            </div>
                                                        </>

                                                    ) : (
                                                        <>
                                                            <p>当前指挥中心：{initData?.control_center?.lat},{initData?.control_center?.lon}</p>
                                                            <p>当前打击区域半径：{initData?.strike_zone_radius}</p>
                                                            <p>当前跟踪区域半径：{initData?.tracking_zone_radius}</p>
                                                            <p>当前监控区域半径：{initData?.surveillance_zone_radius}</p>
                                                            <button onClick={handleEdit}>编辑</button>
                                                        </>
                                                    )}
                                                </>
                                            </div>
                                        ) : (
                                            <div
                                                style={{ color: colorObj[item.chatType] || '#FFFFFF' }}
                                                className='receiver-content helper-content'
                                            >
                                                {item.isQuestData ? (
                                                    <div className='task-content'>
                                                        {item.isStreaming ? (
                                                            <div
                                                                className="streaming-content"
                                                                style={{ whiteSpace: 'pre-line' }}
                                                                dangerouslySetInnerHTML={{ __html: item.displayContent || '思考中...' }}
                                                            />
                                                        ) : (
                                                            <>
                                                                <p style={{ fontWeight: 'bold', textAlign: 'center' }}>方案名称</p>
                                                                <p> &nbsp;&nbsp;&nbsp;&nbsp;{item.content?.plan_name}</p>
                                                                <p style={{ fontWeight: 'bold', textAlign: 'center' }}>方案内容</p>
                                                                <p>&nbsp;&nbsp;&nbsp;&nbsp; {item.content?.plan_desc}</p>
                                                                <p style={{ fontWeight: 'bold', textAlign: 'center' }}>方案详情</p>
                                                                <p>&nbsp;&nbsp;&nbsp;&nbsp; {item.content?.plan_rationality}</p>
                                                            </>
                                                        )}
                                                    </div>
                                                ) : (
                                                    item.content
                                                )}
                                            </div>
                                        )}
                                    </>
                                ) : (
                                    <>
                                        <div style={{ color: '#FFFFFF' }} className='receiver-content user-content'>
                                            {item.content}
                                        </div>
                                        <div className='avatar' style={{ backgroundColor: '#faad14' }}>
                                            用户
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
            <div className='bottom'>
                <Input
                    placeholder='请输入...'
                    value={content}
                    onChange={e => {
                        setContent(e.target.value.replace(/\\n/g, ''));
                    }}
                    onPressEnter={e => {
                        e.preventDefault();
                        send();
                    }}
                />
                <Select
                    defaultValue="Qwen-72b"
                    style={{ width: 120, paddingLeft: 3 }}
                    onChange={(value) => {
                        setModelType(value)
                    }}
                    options={[
                        { value: 'Qwen-72b', label: 'Qwen-72b' },
                        { value: 'deepseek-r1-distill-qwen-7b', label: 'deepseek-7b' },
                    ]}
                />
                {audioStatus ? (
                    <Tooltip title="结束说话">
                        <PauseCircleFilled
                            className='audio-icon pause'
                            onClick={onAudioPlay}
                        />
                    </Tooltip>
                ) : (
                    <Tooltip title="开始说话">
                        <AudioFilled
                            className='audio-icon'
                            onClick={onAudioPlay}
                        />
                    </Tooltip>
                )}

                <div className='bottom_right'>

                    <button
                        className='send'
                        onClick={e => {
                            e.preventDefault();
                            send();
                        }}
                    >
                        发送
                    </button>
                </div>
            </div>
            {
                spinning && (
                    <div className='all-spinning'>
                        <Spin tip='Loading...' spinning={spinning} size='large' />
                    </div>
                )
            }
        </div >
    );
};

export default Chat;
