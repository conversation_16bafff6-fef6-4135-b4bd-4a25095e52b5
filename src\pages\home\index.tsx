import React, { memo } from 'react';
import Content from '../../components/Content';
import Chat from '../../components/Chat';
import LeafletMap from '../../components/LeafletMap'; //静态地图
import './index.scss';
import Right from '../../components/Right';


const HomeContent: React.FC = memo(props => {

    return (
        <>
            <Content>
                
                <div className='title'>跨域无人集群智能生成方案系统</div>
                <div className='title-right'>中船七〇一所科技创新中心</div>
                <div className='home'>
                    <div className='home-left'>
                        <Chat />
                    </div>
                    <div className='home-right'>
                        <Right/>
                        {/* <LeafletMap /> */}
                        {/* <Bmap
                            // data={pointsValue}
                            data={[]}
                        /> */}
                        {/* <div className='img' style={{backgroundImage: `url(${mapUrl})`}} ></div> */}
                    </div>
                </div>
            </Content>
        </>
    );
});
export default HomeContent;
