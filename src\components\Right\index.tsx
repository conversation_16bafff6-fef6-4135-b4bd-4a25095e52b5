import { FC, Key, useEffect, useRef, useState } from 'react';
import './index.scss';
import { Col, Row, Table, Tag } from 'antd';
import type { TableProps } from 'antd';

import { api } from '../../api';
import { useRecoilState, useRecoilValue } from 'recoil';
import Gau<PERSON><PERSON><PERSON> from '../GaugeChart';

import '../../styles/custom.scss'
import { taskDataAtom } from '../../state';

interface DataType {
    sensor_info: readonly SensorData[] | undefined;
    wq_info: readonly WeaponData[] | undefined;
    sendUnmannedSystemNodeCode: any;
    key: string;
    name: string;
    type: string;
    address: string;
    // tags: string[];
}
interface WeaponData {
    name: string;
    max_range: number;
    min_range: number;
    powered: boolean;
    quantity: number;
}
interface SensorData {
    sensor_name: string;
    max_range: number;
    min_range: number;
    powered: boolean;
}
interface TaskDataType {
    force_allocation: any;
    tactical_understand: string;
    tactical_target: string;
    id: any;
    key: string;
    task_name: string;
    task_target: string;
    task_desc: string;
    task_time: string;
    task_type: string;
    task_instance: string;
}
interface OriginalData {
    red: Array<{
        longitude: any;
        latitude: any;
        [key: string]: any;
    }>;
}

interface ProcessedData {
    red: Array<{
        position: string;
        [key: string]: any;
    }>;
}

// 数据处理函数
const processRedData = (originalData: OriginalData): ProcessedData => {
    // 处理单个阵营数据
    const processFaction = (faction: OriginalData['red']) =>
        faction.map(item => ({
            ...item,
            position: `${item.longitude}, ${item.latitude}`
        }));

    return {
        red: processFaction(originalData.red)
    };
};


const processBlueData = (blueData: any) => {
    return blueData.flatMap((item: { detectingTargetSequence: any[]; }) =>
        item.detectingTargetSequence.map(target => ({
            id: target.targetId,
            position: `${target.targetLongitude}, ${target.targetLatitude}`, // 经度在前纬度在后的数组格式
            heading: target.targetHeading,
            type: target.type,
            platform_name: target.platform_name,
            targetSpeed: target.targetSpeed,
        }))
    );
};

const Right: FC = () => {

    const [data, setData] = useState<any>()
    const [enemyData, setEnemyData] = useState<any>()
    const [expandedRowKeys, setExpandedRowKeys] = useState<any>([])
    const [expandedWeaponRowKeys, setExpandedWeaponRowKeys] = useState<any>([])
    // const taskData:any = useRecoilValue(taskDataAtom)
    // const [taskData, setTaskData] = useRecoilState(taskDataAtom);
    const [taskData, setTaskData] = useState();
    const [taskProgress, setTaskProgress] = useState<any>([]);

    //字段翻译
    const translateTaskTypes = (tasks: any[]) => {
        // 创建中英任务类型映射表
        const taskTypeMap: { [key: string]: string } = {
            'patrol': '巡逻',
            'track': '跟踪',
            'attack': '打击'
        };

        // 创建深拷贝避免修改原始数据
        return tasks.map(task => ({
            ...task,
            task_type: taskTypeMap[task.task_type] || task.task_type // 保持未匹配类型不变
        }));
    };

    const fetchData = async () => {
        await api
            .getSimTableData()
            .then(res => {
                const redData = processRedData(res?.data)?.red
                const blueData = processBlueData(res?.data?.blue);
                // console.log(processedBlue, 'blue');
                setEnemyData(blueData)
                setData(redData)
            })
            .catch(err => {
                console.log(err);
            })
    };
    const fetchTaskData = async () => {
        await api
            .getTaskTableData()
            .then(res => {
                const plan_tasks: any = translateTaskTypes(res?.data?.plan_tasks)
                setTaskData(plan_tasks)
            })
            .catch(err => {
                console.log(err);
            })
    };
    const fetchTaskProgress = async () => {
        try {
            const res = await api.getTaskProgress();
            // console.log('任务进度数据:', res?.data);
            // 直接使用后端返回的数据，确保数据格式正确
            if (res?.data && Array.isArray(res.data)) {
                // 处理每个任务的完成度数据
                const processedData = res.data.map((task: any) => ({
                    ...task,
                    // 确保任务名称存在
                    task_name: task.task_name || '未命名任务',
                    // 确保完成度是数字并保留两位小数
                    task_complet_avg: typeof task.task_complet_avg === 'number'
                        ? Number(task.task_complet_avg.toFixed(2))
                        : 0
                }));

                setTaskProgress(processedData);
            }
        } catch (err) {
            console.log('获取任务进度出错:', err);
        }
    };

    // 轮询获取数据
    useEffect(() => {
        // 立即获取第一次数据
        fetchData();
        fetchTaskData();
        fetchTaskProgress();

        // 设置轮询间隔
        const intervalId = setInterval(fetchData, 3000);
        const intervalTaskId = setInterval(fetchTaskData, 3000);
        const intervalTaskProgressId = setInterval(fetchTaskProgress, 2000); // 稍微降低频率，减少可能的闪烁

        // 清除定时器
        return () => {
            clearInterval(intervalId);
            clearInterval(intervalTaskId);
            clearInterval(intervalTaskProgressId);
        };
    }, []);



    const enemyColumns: TableProps<DataType>['columns'] = [
        {
            title: '探测目标标识',
            dataIndex: 'id',
            key: 'id',
            align: 'center'
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center'
        },
        {
            title: '位置',
            dataIndex: 'position',
            key: 'posiyion',
            align: 'center'
        },
        {
            title: '艏向角',
            dataIndex: 'heading',
            key: 'heading',
            align: 'center'
        },
        {
            title: '速度',
            dataIndex: 'targetSpeed',
            key: 'targetSpeed',
            align: 'center'
        },
    ];

    const columns: TableProps<DataType>['columns'] = [
        {
            title: '实体编号',
            dataIndex: 'sendUnmannedSystemNodeCode',
            key: 'sendUnmannedSystemNodeCode',
            align: 'center'
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center'
        },
        // {
        //     title: '平台名',
        //     dataIndex: 'platform_name',
        //     key: 'platform_name',
        //     align: 'center'
        // },
        {
            title: '位置',
            dataIndex: 'position',
            key: 'position',
            align: 'center'
        },
        {
            title: '艏向角',
            dataIndex: 'absoluteHeading',
            key: 'absoluteHeading',
            align: 'center'
        },
        {
            title: '速度',
            dataIndex: 'absoluteSpeed',
            key: 'absoluteSpeed',
            align: 'center'
        },
    ];
    const taskColumns: TableProps<TaskDataType>['columns'] = [
        {
            title: '任务名称',
            dataIndex: 'task_name',
            key: 'task_name',
            align: 'center'
        },
        {
            title: '任务目标',
            dataIndex: 'task_target',
            key: 'task_target',
            align: 'center'
        },
        {
            title: '任务内容',
            dataIndex: 'task_desc',
            key: 'task_desc',
            align: 'center'
        },
        {
            title: '预估时段',
            dataIndex: 'task_time',
            key: 'task_time',
            align: 'center'
        },
        {
            title: '任务类型',
            dataIndex: 'task_type',
            key: 'task_type',
            align: 'center'
        },
    ];
    const redWeaponColumns: TableProps<WeaponData>['columns'] = [
        {
            title: '武器名称',
            dataIndex: 'name',
            key: 'name',
            align: 'center'
        },
        {
            title: '最大范围',
            dataIndex: 'max_range',
            key: 'max_range',
            align: 'center'
        },
        // {
        //     title: '最小范围',
        //     dataIndex: 'min_range',
        //     key: 'min_range',
        //     align: 'center'
        // },
        {
            title: '剩余数量',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'center'
        },
        {
            title: '是否开机',
            dataIndex: 'powered',
            key: 'powered',
            align: 'center',
            render: (powered) => (
                <Tag color={powered }>
                    {powered ? '是' : '否'}
                </Tag>
            )
        },
    ];
    const redSensorColumns: TableProps<SensorData>['columns'] = [
        {
            title: '传感器名称',
            dataIndex: 'sensor_name',
            key: 'sensor_name',
            align: 'center'
        },
        {
            title: '最大范围',
            dataIndex: 'max_range',
            key: 'max_range',
            align: 'center'
        },
        {
            title: '最小范围',
            dataIndex: 'min_range',
            key: 'min_range',
            align: 'center'
        },
        {
            title: '是否开机',
            dataIndex: 'powered',
            key: 'powered',
            align: 'center',
            render: (powered) => (
                <Tag color={powered}>
                    {powered ? '是' : '否'}
                </Tag>
            )
        }
    ];

    return (
        <div className='content'>
            <div className='table-dom custom-styles'>
                <div style={{ textAlign: 'center', color: 'white', margin: '6px' }}>我方单位</div>
                <Table
                    rowKey={'sendUnmannedSystemNodeCode'}
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    expandable={{
                        onExpand: (expanded, record) => {
                            console.log(record.wq_info);

                            if (expanded == true) setExpandedWeaponRowKeys([record.sendUnmannedSystemNodeCode]);
                            if (expanded == false) setExpandedWeaponRowKeys([]);
                        },
                        // expandedRowRender: record => {
                        //     return <>
                        //         <Table
                        //             columns={redWeaponColumns}
                        //             dataSource={record.wq_info}
                        //             pagination={false}
                        //         />
                        //         <Table
                        //             columns={redSensorColumns}
                        //             dataSource={record.sensor_info}
                        //             pagination={false}
                        //         />
                        //     </>;
                        // },
                        expandedRowKeys: expandedWeaponRowKeys,
                    }}
                    size='middle' />
            </div>
            <div className='table-dom custom-styles'>
                <div style={{ textAlign: 'center', color: 'white', margin: '6px' }}>已发现敌方单位</div>
                <Table
                    dataSource={enemyData}
                    columns={enemyColumns}
                    pagination={false}
                    size='middle' />
            </div>
            <div className='table-dom custom-styles'>
                <div style={{ textAlign: 'center', color: 'white', margin: '6px' }}>当前任务</div>
                <Table
                    rowKey={'id'}
                    columns={taskColumns}
                    dataSource={taskData}
                    pagination={false}
                    expandable={{
                        onExpand: (expanded, record) => {
                            // console.log(expanded, record);
                            if (expanded == true) setExpandedRowKeys([record.id]);
                            if (expanded == false) setExpandedRowKeys([]);
                            // console.log(expandedRowKeys);
                        },
                        expandedRowRender: record => {
                            return <>
                                <p style={{ margin: 0 }}>{record.tactical_target}</p>
                                <p style={{ margin: 0 }}>{record.tactical_understand}</p>
                                <p style={{ margin: 0 }}>{record.task_instance}</p>
                                {record.force_allocation.map((f: any) =>
                                    <p>派遣我方兵力编号:{f.red?.join(',')}，敌方目标编号:{f.blue}</p>
                                )}
                            </>;
                        },
                        expandedRowKeys: expandedRowKeys,
                    }}
                    size='middle' />
            </div>
            <div className='table-dom'>
                <div style={{ textAlign: 'center', color: 'white', margin: '4px' }}>任务执行进度</div>
                {Array.isArray(taskProgress) && taskProgress.length > 0 ? (
                    taskProgress.reduce((rows: any[][], task: any, index: number) => {
                        if (index % 3 === 0) rows.push([]);
                        rows[rows.length - 1].push(task);
                        return rows;
                    }, []).map((rowTasks: any[], rowIndex: Key | null | undefined) => (
                        <Row gutter={[16, 16]} key={rowIndex}>
                            {rowTasks.map((task, colIndex) => (
                                <Col span={8} key={`${task.task_name}-${colIndex}`}>
                                    <GaugeChart
                                        value={task.task_complet_avg}
                                        name={task.task_name}
                                    />
                                </Col>
                            ))}
                        </Row>
                    ))
                ) : (
                    <div style={{ textAlign: 'center', color: 'white' }}>暂无任务进度数据</div>
                )}
            </div>
        </div>
    );
};

export default Right;
