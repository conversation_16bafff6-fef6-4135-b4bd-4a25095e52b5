.home {
    display: flex;
    justify-content: space-between;
    width: 100vw;

    >div {
        height: 96vh;
        // background: rgba(10,77,112,0.35);
        background: #018f8f;
        border: 1px solid #2D2C3C;
        padding: 12px;
    }

    >div:first-child {
        width: 40%;
    }

    >div:nth-child(2) {
        width: 59%;
    }

    .home-right .img {
        background-repeat: no-repeat;
        background-size: contain;
        width: 100%;
        height: 100%;
    }
}

.content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .next-step {
        width: 60px;
        text-align: center;
        cursor: pointer;
        font-weight: bold;
        
        a {
            text-decoration: none;
            color: #FFFFFF;
        }

        a:hover {
            color: aqua
        }

    }
}

.title {
    text-align: left;
    color: white;
    width:40vw;
    font-size: 26px;
    margin-left:10px ;
}

.title-right {
    text-align: right;
    color: white;
    width: 40vw;
    font-size: 26px;    
    margin-right:10px ;
}   