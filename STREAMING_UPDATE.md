# 作战筹划流式输出更新说明

## 更新内容

本次更新将前端的作战筹划输出从模拟流式输出改为真正的流式输出，同时保持临机决策功能不变。

## 主要修改

### 1. API层修改 (`src/api/index.ts`)

- 新增 `postVoiceStream` 方法，支持真正的流式数据接收
- 使用 Fetch API 和 ReadableStream 处理服务器发送事件 (SSE)
- 支持实时数据解析和回调处理

### 2. 前端组件修改 (`src/components/Chat/index.tsx`)

- 新增 `handleRealStreaming` 函数处理真正的流式输出
- 新增 `generatePlanHTML` 函数生成方案HTML内容
- 修改 `send` 函数，区分处理不同类型的请求：
  - **临机决策**: 保持原有逻辑不变
  - **任务终止**: 保持原有逻辑不变  
  - **作战筹划**: 使用新的流式输出

### 3. 后端测试接口 (`api.py`)

- 新增 `/701api/control_center/getvoice_stream` 接口
- 模拟分步骤的流式数据生成
- 支持 Server-Sent Events (SSE) 格式

## 功能特点

### 流式输出优势

1. **实时反馈**: 用户可以看到方案生成的实时进度
2. **更好的用户体验**: 不需要等待完整响应，可以逐步查看内容
3. **降低感知延迟**: 即使总时间相同，用户感觉更快
4. **支持长时间处理**: 适合复杂的AI生成任务

### 保持兼容性

1. **临机决策不变**: 完全保持原有的处理逻辑和用户界面
2. **错误回退**: 如果流式传输失败，自动回退到普通请求
3. **数据格式兼容**: 最终数据格式与原有格式保持一致

## 测试方法

### 1. 启动前端开发服务器

```bash
npm start
```

### 2. 测试流式输出

1. 在聊天界面选择 "Qwen-72b" 或 "deepseek-r1-distill-qwen-7b" 模型
2. 输入任何作战筹划相关的问题
3. 观察方案内容的实时生成过程
4. 打开浏览器开发者工具的Network标签，可以看到流式数据传输

### 3. 测试临机决策（确保不受影响）

1. 在聊天界面选择 "临机决策" 模型
2. 输入决策相关的问题
3. 确认功能与之前完全一致

### 4. 调试信息

- 在浏览器控制台中可以看到 "Received chunk:" 的调试日志
- 在Network标签中可以看到流式数据的传输过程
- 如果流式传输失败，会自动回退到普通请求

## 技术实现细节

### 流式数据格式

后端返回的是Server-Sent Events (SSE)格式的流式数据：

```
data:
data: [
data:     {
data:         "battlefield_analysis": "首长
data: 意图理解,核心作战目标
data: 为针对敌方无人
data: 艇进行作战筹划，
...
data:  }
data: ]
event: stats
data: 59.30
event: end
data:
```

### 数据处理逻辑

1. **数据累积**: 将所有接收到的数据块累积到缓冲区
2. **缓冲处理**: 改进了SSE数据解析，正确处理行分割和数据完整性
3. **节流更新**: 使用50ms节流避免过于频繁的DOM更新
4. **JSON解析**: 持续尝试解析累积的数据为完整JSON
5. **实时显示**:
   - JSON解析成功时：使用结构化数据生成格式化的HTML
   - JSON解析失败时：显示原始文本内容（保持流式效果）
6. **格式化**: 处理换行、制表符等特殊字符

### 修复的问题

1. **数据丢失**: 改进了SSE数据解析，确保不会丢失任何数据块
2. **更新频率**: 添加了节流机制，避免过于频繁的DOM更新导致性能问题
3. **缓冲处理**: 正确处理不完整的数据行，确保数据完整性
4. **调试支持**: 添加了详细的调试日志，便于排查问题

### 错误处理

- 网络错误自动回退到普通请求
- JSON解析错误会被捕获并记录
- 流式传输中断会优雅降级

### 性能优化

- 使用 ReadableStream 避免内存积累
- 实时更新DOM，避免大量重绘
- 保持原有的滚动和交互逻辑

## 注意事项

1. **后端接口**: 实际部署时需要后端提供真正的流式接口
2. **网络环境**: 流式输出对网络稳定性要求较高
3. **浏览器兼容**: 现代浏览器都支持 Fetch 和 ReadableStream
4. **临机决策**: 确保临机决策功能完全不受影响

## 后续优化建议

1. 添加流式传输进度指示器
2. 支持流式传输的暂停和恢复
3. 优化大数据量的流式处理性能
4. 添加更详细的错误处理和用户提示
