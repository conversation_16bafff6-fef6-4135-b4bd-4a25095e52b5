import './axios';
import axios from 'axios';

interface Response<T> {
    code: 200 | 500;
    msg: string;
    data: T;
}

export interface Ratio {
    [key: string]: number;
}

export interface DevDataInfo {
    name: string,
    sex: string,
    age: number,
}

export interface OtherInfo {
    otherProps: Ratio[];
}

export const api = {
    // getPicture
    getPicture: async (): Promise<any> => {
        const { data: resData } = await axios.get('/api/picture');
        return resData;
    },

    // getPicture
    getMapData: async (): Promise<any> => {
        const { data: resData } = await axios.get('/api/map');
        return resData;
    },

    // getDialogue
    getDialogue: async (input: string): Promise<any> => {
        const { data: resData } = await axios.post(`/api/dialogue?input=${input}`);
        return resData;
    },
    // getTableData
    getSimTableData: async (): Promise<any> => {
        const { data: resData } = await axios.get(`/701api/control_center/getsim`);
        return resData;
    },
    // getTableData
    getTaskTableData: async (): Promise<any> => {
        const { data: resData } = await axios.get(`/701api/control_center/taskdata`);
        return resData;
    },


    // 与仿真交互
    postSimulate: async (): Promise<any> => {
        const data = await axios.post('/api/start_plan');
        return data;
    },

    // 智能语音系统
    // 语音识别成文字
    getChatView: async (): Promise<any> => {
        const data = await axios.get(`/701api/control_center/chatview`);
        return data;
    },
    // getvoice - 修改为支持流式输出
    postVoice: async (params: any, onData?: (chunk: string) => void): Promise<any> => {
        // 如果提供了 onData 回调，使用流式处理
        if (onData) {
            const response = await fetch(`/701api/control_center/getvoice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('Response body is not readable');
            }

            const decoder = new TextDecoder();
            let buffer = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 将新数据添加到缓冲区
                    buffer += decoder.decode(value, { stream: true });

                    // 按行分割处理
                    const lines = buffer.split('\n');
                    // 保留最后一行（可能不完整）
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.trim() === '') continue;

                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data.trim() === '[DONE]') {
                                return;
                            }
                            // 不过滤空数据，因为可能是有意义的空格或换行
                            try {
                                onData(data);
                            } catch (e) {
                                console.error('Error processing SSE data:', e);
                            }
                        } else if (line.startsWith('event: ')) {
                            // 处理事件类型，但不传递给回调
                            const eventType = line.slice(7).trim();
                            console.log('SSE Event:', eventType);
                        }
                    }
                }

                // 处理缓冲区中剩余的数据
                if (buffer.trim()) {
                    const line = buffer.trim();
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim() !== '[DONE]') {
                            try {
                                onData(data);
                            } catch (e) {
                                console.error('Error processing final SSE data:', e);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        } else {
            // 如果没有提供回调，使用原有的 axios 方式（用于临机决策等）
            const { data: resData } = await axios.post(`/701api/control_center/getvoice`, params);
            return resData;
        }
    },
    // immediate_decision
    immediateDecision: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/decisionmaking`, params);
        return resData;
    },
    // immediate_decision
    taskTerminated: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/exittask`, params);
        return resData;
    },
    // immediate_decision
    sendDecision: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(
            `/701api/control_center/senddecision`,
            params,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
        return resData;
    }, 
    // 语音转文字
    getVoiceData: async (): Promise<any> => {
        const { data: resData } = await axios.get(`/701api/control_center/getvoicedata`);
        return resData;
    },

    //修改初始信息
    // editInitData
    editInitData: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/chatview`, params);
        return resData;
    },
    //获取初始信息
    // getInitData
    getInitData: async (): Promise<any> => {
        const { data: resData } = await axios.get(`/701api/control_center/chatview`);
        return resData;
    },
    // getInitData
    sendPlanData: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/sendplan`, params);
        return resData;
    },
    // 兵力部署
    forceDeploy: async (params: any): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/forcedeploy`, params);
        return resData;
    },
    // 语音
    sendVoice: async (formData: FormData): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/speechinput`, formData);
        return resData;
    },
    // 任务进度
    getTaskProgress: async (): Promise<any> => {
        const { data: resData } = await axios.post(`/701api/control_center/taskdata`);
        return resData;
    },
};
