import { FC, useCallback, useEffect, useRef, useState } from 'react';
import './index.scss';
import { Input, Button, Spin, message, Tooltip, Select, Col, Row, Table } from 'antd';
import { PlayCircleOutlined, AudioFilled, PauseCircleFilled } from '@ant-design/icons';
import { jsonrepair } from 'jsonrepair';

import { api } from '../../api';
import { taskDataAtom } from '../../state';
import { useRecoilState } from 'recoil';

/**
 * 增强的 JSON 修复机制说明：
 *
 * 本组件集成了 jsonrepair 库，提供强大的流式 JSON 解析和修复能力：
 *
 * 1. 智能修复策略：
 *    - 自动补全缺失的括号和引号
 *    - 处理悬挂的逗号
 *    - 修复不完整的字符串
 *    - 智能推断 JSON 结构
 *
 * 2. 多层次容错：
 *    - 完整 JSON 解析
 *    - jsonrepair 智能修复
 *    - 自定义部分解析
 *    - 原始数据显示
 *
 * 3. 实时流式处理：
 *    - 50ms 节流更新
 *    - 渐进式内容渲染
 *    - 错误恢复机制
 *
 * 使用场景：
 * - 大模型流式输出的 JSON 数据
 * - 网络传输中断导致的不完整数据
 * - 格式错误的 JSON 字符串
 */

interface IProps {
    sendChatMsg?: (data: any) => void;
}

const colorObj: any = {
    success: '#FFFFFF',
    waiting: 'yellow',
    error: 'red',
    text: '#FFFFFF',
};

// 添加对象转字符串的函数
const formatObjectToString = (obj: any): string => {
    // 对于 null 和 undefined
    if (obj === null) return 'null';
    if (obj === undefined) return 'undefined';

    // 基本类型直接转换为字符串
    if (typeof obj === 'string') return obj;
    if (typeof obj === 'number' || typeof obj === 'boolean') return obj.toString();

    // 处理数组类型
    if (Array.isArray(obj)) {
        const items = obj.map(item => formatObjectToString(item));
        return `[${items.join(', ')}]`;
    }

    // 处理对象类型
    if (typeof obj === 'object') {
        const entries = Object.entries(obj);
        const formattedEntries = entries.map(([key, value]) => {
            if (value === "") {
                return `${key}: ""`;  // 修改这里：空字符串输出双引号
            } else {
                return `${key}: ${formatObjectToString(value)}`;
            }
        });
        return `{${formattedEntries.join(', ')}}`;
    }

    // 其他类型使用默认的 toString 方法
    return obj.toString();
};


const Chat: FC<IProps> = props => {
    // const [mapUrl, setMapUrl] = useRecoilState(mapUrlAtom);
    const [taskData, setTaskData] = useRecoilState(taskDataAtom);
    const [resSuccess, setResSuccess] = useState(false);
    const [initData, setInitData] = useState<any>();
    const [initRectangleData, setInitRectangleData] = useState<any>();
    // 编辑状态
    const [isEditing, setIsEditing] = useState(false);
    const [isRectangleEditing, setIsRectangleEditing] = useState(false);
    // 临时存储编辑数据
    const [tempData, setTempData] = useState<any>();
    const [tempRectangleData, setRectangleTempData] = useState<any>();
    const [shape, setShape] = useState<boolean>(true);
    // sendId 1-助手；2-用户
    const [messageList, setMessageList] = useState<any[]>([
        {
            msgId: Date.now() + Math.random() * 10000,
            sendId: '1',
            content: '',
            displayContent: '',
            chatType: 'toolptip',
            // hasButton: false,
        },
    ]);

    const [content, setContent] = useState<string>('');
    const messagesEnd = useRef<HTMLDivElement>(null);

    const [spinning, setSpinning] = useState(false);

    // const [streamContent, setStreamContent] = useState('');
    // const [isStreaming, setIsStreaming] = useState(false);
    // const [planData, setPlanData] = useState();
    // const [displayContent, setDisplayContent] = useState('');
    const streamIntervalRef = useRef<any>(null);
    const responseDataRef = useRef<any>([]);
    const scrollToBottom = () => {
        setTimeout(() => {
            const chatBox: any = document.getElementById('chatItems');
            if (chatBox) {
                chatBox.scrollTop = chatBox.scrollHeight;
            }
        }, 100); // 增加延迟，确保DOM更新完成
    };

    // 进入编辑模式
    const handleEditContent = (index: number) => {
        setMessageList(prev => prev.map((msg, i) => {
            if (i === index) {
                // 如果是决策数据，使用原始数据的JSON字符串作为编辑内容
                if (msg.isDecisionData) {
                    // 使用漂亮的格式化输出JSON
                    const formattedJson = JSON.stringify(msg.rawDecisionData, null, 2);
                    return {
                        ...msg,
                        isEditing: true,
                        editContent: formattedJson
                    };
                } else {
                    // 普通消息编辑
                    return {
                        ...msg,
                        isEditing: true,
                        editContent: msg.content
                    };
                }
            }
            return msg;
        }));

        // 滚动到底部，确保编辑区域可见
        setTimeout(() => {
            scrollToBottom();
        }, 100);
    };

    // 取消编辑
    const handleCancelEdit = (index: number) => {
        setMessageList(prev => prev.map((msg, i) =>
            i === index ? { ...msg, isEditing: false } : msg
        ));
    };

    // 编辑内容变化
    const handleContentEdit = (e: React.ChangeEvent<HTMLTextAreaElement>, index: number) => {
        setMessageList(prev => prev.map((msg, i) =>
            i === index ? {
                ...msg,
                editContent: e.target.value
            } : msg
        ));
    };

    // 保存决策编辑
    const handleSaveDecisionEdit = (index: number) => {
        setMessageList(prev => {
            const newList = [...prev];
            if (newList[index]) {
                try {
                    // 尝试解析编辑内容为JSON
                    const parsedContent = JSON.parse(newList[index].editContent);

                    // 更新决策数据，但不发送
                    newList[index] = {
                        ...newList[index],
                        isEditing: false, // 退出编辑状态
                        rawDecisionData: parsedContent // 直接存储解析后的数据
                    };

                    message.success('修改已保存，点击"发送决策"按钮发送到后端');
                } catch (error) {
                    // JSON解析错误
                    message.error('JSON格式错误，请检查输入');
                    console.error('JSON解析错误:', error);
                    return prev; // 保持原状态不变
                }
            }
            return newList;
        });

        // 滚动到底部，确保内容可见
        setTimeout(() => {
            scrollToBottom();
        }, 100);
    };

    // 保存内容修改（兼容旧逻辑）
    // const handleSaveContent = (index: number) => {
    //     setMessageList(prev => {
    //         const newList = [...prev];
    //         if (newList[index]) {
    //             try {
    //                 // 尝试解析编辑内容为JSON
    //                 const parsedContent = JSON.parse(newList[index].editContent);

    //                 if (newList[index].isDecisionData) {
    //                     // 调用新的保存函数
    //                     handleSaveDecisionEdit(index);
    //                     return prev; // 由handleSaveDecisionEdit处理
    //                 } else {
    //                     // 普通消息更新
    //                     newList[index] = {
    //                         ...newList[index],
    //                         isEditing: false,
    //                         content: newList[index].editContent
    //                     };

    //                     // 原有的JSON格式化和发送逻辑
    //                     const formattedJson = formatAsJson(newList[index].editContent);
    //                     api.sendDecision(formattedJson);
    //                     console.log(formattedJson);
    //                 }
    //             } catch (error) {
    //                 // JSON解析错误
    //                 message.error('JSON格式错误，请检查输入');
    //                 console.error('JSON解析错误:', error);
    //                 return prev; // 保持原状态不变
    //             }
    //         }
    //         return newList;
    //     });
    // };

    // // 将内容格式化为有效的JSON字符串，并处理空值
    // const formatAsJson = (content: string): string => {
    //     try {
    //         // 尝试解析内容为JSON对象
    //         const parsed = JSON.parse(content);

    //         // 递归处理对象和数组中的空值
    //         const replaceEmptyValues = (obj: any): any => {
    //             if (obj === null || obj === undefined) {
    //                 return '';
    //             }

    //             if (Array.isArray(obj)) {
    //                 return obj.map(item => replaceEmptyValues(item));
    //             }

    //             if (typeof obj === 'object') {
    //                 const newObj: Record<string, any> = {};
    //                 for (const key in obj) {
    //                     if (obj.hasOwnProperty(key)) {
    //                         newObj[key] = replaceEmptyValues(obj[key]);
    //                     }
    //                 }
    //                 return newObj;
    //             }

    //             return obj;
    //         };

    //         // 处理空值
    //         const cleaned = replaceEmptyValues(parsed);

    //         // 返回带格式的JSON字符串
    //         return JSON.stringify(cleaned, null, 2);
    //     } catch (e) {
    //         try {
    //             // 如果解析失败，尝试将内容视为字符串值
    //             return JSON.stringify({ content }, null, 2);
    //         } catch (e2) {
    //             // 如果仍然失败，返回默认的错误JSON
    //             return JSON.stringify({
    //                 error: "Invalid content format",
    //                 original: content.substring(0, 100) + (content.length > 100 ? "..." : "")
    //             }, null, 2);
    //         }
    //     }
    // };

    // 进入编辑模式
    const handleEdit = (value: any) => {
        if (shape) {
            setTempData({
                ...value,
                lat: value?.control_center?.lat,    // 新增：提取嵌套数据
                lon: value?.control_center?.lon,
            });
            setIsEditing(true);
        } else {
            setRectangleTempData({
                ...value,
                surveillance_minlat: value?.surveillance?.minlat,
                surveillance_maxlat: value?.surveillance?.maxlat,
                surveillance_minlon: value?.surveillance?.minlon,
                surveillance_maxlon: value?.surveillance?.maxlon,
                tracking_minlat: value?.tracking?.minlat,
                tracking_maxlat: value?.tracking?.maxlat,
                tracking_minlon: value?.tracking?.minlon,
                tracking_maxlon: value?.tracking?.maxlon,
                strike_minlat: value?.strike?.minlat,
                strike_maxlat: value?.strike?.maxlat,
                strike_minlon: value?.strike?.minlon,
                strike_maxlon: value?.strike?.maxlon,
            });
            setIsRectangleEditing(true);
        }
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        setIsRectangleEditing(false);
    };

    // 保存修改
    const handleSave = () => {
        if (shape) {
            // 圆形配置保存
            const transformed = {
                circle: {
                    control_center: {
                        lat: parseFloat(tempData.lat),     // 保持浮点型转换
                        lon: parseFloat(tempData.lon)      // 保证地理坐标精度
                    },
                    // 直接使用原始值，保留数据类型
                    strike_zone_radius: tempData.strike_zone_radius,
                    surveillance_zone_radius: tempData.surveillance_zone_radius,
                    tracking_zone_radius: tempData.tracking_zone_radius
                },
                rectangle: initRectangleData || {
                    surveillance: { minlat: 0, maxlat: 0, minlon: 0, maxlon: 0 },
                    tracking: { minlat: 0, maxlat: 0, minlon: 0, maxlon: 0 },
                    strike: { minlat: 0, maxlat: 0, minlon: 0, maxlon: 0 }
                }
            };
            // console.log(transformed);
            editInitData(transformed); // 调用保存函数
            setIsEditing(false);
            getInitData();
        } else {
            // 矩形配置保存
            const rectangle_transformed = {
                circle: initData || {
                    control_center: { lat: 0, lon: 0 },
                    strike_zone_radius: 0,
                    surveillance_zone_radius: 0,
                    tracking_zone_radius: 0
                },
                rectangle: {
                    surveillance: {
                        minlat: parseFloat(tempRectangleData.surveillance_minlat),
                        maxlat: parseFloat(tempRectangleData.surveillance_maxlat),
                        minlon: parseFloat(tempRectangleData.surveillance_minlon),
                        maxlon: parseFloat(tempRectangleData.surveillance_maxlon),
                    },
                    tracking: {
                        minlat: parseFloat(tempRectangleData.tracking_minlat),
                        maxlat: parseFloat(tempRectangleData.tracking_maxlat),
                        minlon: parseFloat(tempRectangleData.tracking_minlon),
                        maxlon: parseFloat(tempRectangleData.tracking_maxlon),
                    },
                    strike: {
                        minlat: parseFloat(tempRectangleData.strike_minlat),
                        maxlat: parseFloat(tempRectangleData.strike_maxlat),
                        minlon: parseFloat(tempRectangleData.strike_minlon),
                        maxlon: parseFloat(tempRectangleData.strike_maxlon),
                    },
                }
            };
            editInitData(rectangle_transformed); // 调用保存函数
            setIsRectangleEditing(false);
            getInitData();
        }
    };

    // 处理输入框修改
    const handleChange = (key: any, value: any) => {
        if (shape) {
            setTempData((prev: any) => ({
                ...prev,
                [key]: value
            }));
        } else {
            setRectangleTempData((prev: any) => ({
                ...prev,
                [key]: value
            }));
        }
    };

    const editInitData = async (value: any) => {
        await api
            .editInitData(value)
            .then(res => {
                // console.log(res);
                setResSuccess(!resSuccess);
            })
            .catch(err => {
                console.log(err);
            })
    }
    const getInitData = async () => {
        await api
            .getInitData()
            .then(res => {
                setInitData(res.data.circle)
                setInitRectangleData(res.data.rectangle)
                // 初始化临时数据，确保编辑时有初始值
                if (res.data.circle) {
                    setTempData({
                        ...res.data.circle,
                        lat: res.data.circle.control_center?.lat,
                        lon: res.data.circle.control_center?.lon,
                    });
                }
                if (res.data.rectangle) {
                    setRectangleTempData({
                        surveillance_minlat: res.data.rectangle.surveillance?.minlat,
                        surveillance_maxlat: res.data.rectangle.surveillance?.maxlat,
                        surveillance_minlon: res.data.rectangle.surveillance?.minlon,
                        surveillance_maxlon: res.data.rectangle.surveillance?.maxlon,
                        tracking_minlat: res.data.rectangle.tracking?.minlat,
                        tracking_maxlat: res.data.rectangle.tracking?.maxlat,
                        tracking_minlon: res.data.rectangle.tracking?.minlon,
                        tracking_maxlon: res.data.rectangle.tracking?.maxlon,
                        strike_minlat: res.data.rectangle.strike?.minlat,
                        strike_maxlat: res.data.rectangle.strike?.maxlat,
                        strike_minlon: res.data.rectangle.strike?.minlon,
                        strike_maxlon: res.data.rectangle.strike?.maxlon,
                    });
                }
            })
            .catch(err => {
                console.log(err);
            })

    }


    // 增强的流式输出处理函数（集成 jsonrepair）
    const handleRealStreaming = async (msgId: number, params: any) => {
        let jsonBuffer = '';
        let finalData: any = null;
        let updateTimeout: number | null = null;
        let repairAttempts = 0;
        let lastSuccessfulParse: any = null;

        // 增强的节流更新函数
        const throttledUpdate = () => {
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }

            updateTimeout = setTimeout(() => {
                // 尝试生成HTML内容（现在使用 jsonrepair）
                const htmlContent = generateStreamingPlanHTML(jsonBuffer);

                // 检查是否应该使用HTML渲染
                const shouldUseHtmlRendering = htmlContent &&
                    htmlContent !== '<p>正在生成方案...</p>' &&
                    htmlContent !== '<p>正在解析数据...</p>';

                if (shouldUseHtmlRendering) {
                    // 使用HTML渲染模式
                    try {
                        // 首先尝试完整解析
                        let parsedData = JSON.parse(jsonBuffer);
                        finalData = parsedData;
                        lastSuccessfulParse = parsedData;
                        responseDataRef.current = parsedData; // 实时更新引用

                        console.log('完整JSON解析成功:', parsedData);

                        // 更新消息列表，使用完整解析的数据
                        setMessageList(prev => prev.map(msg => {
                            if (msg.msgId === msgId) {
                                return {
                                    ...msg,
                                    displayContent: htmlContent,
                                    content: parsedData,
                                    isStreaming: true
                                };
                            }
                            return msg;
                        }));

                    } catch (jsonError) {
                        // 完整解析失败，尝试使用 jsonrepair
                        try {
                            repairAttempts++;
                            const repaired = jsonrepair(jsonBuffer);
                            let repairedData;

                            if (typeof repaired === 'string') {
                                repairedData = JSON.parse(repaired);
                            } else {
                                repairedData = repaired;
                            }

                            lastSuccessfulParse = repairedData;
                            responseDataRef.current = repairedData; // 实时更新引用
                            console.log(`jsonrepair 修复成功 (尝试 ${repairAttempts}):`, repairedData);

                            setMessageList(prev => prev.map(msg => {
                                if (msg.msgId === msgId) {
                                    return {
                                        ...msg,
                                        displayContent: htmlContent,
                                        content: repairedData,
                                        isStreaming: true
                                    };
                                }
                                return msg;
                            }));

                        } catch (repairError) {
                            console.warn('jsonrepair 修复失败:', repairError);

                            // 如果修复也失败，但有HTML内容，仍然显示
                            setMessageList(prev => prev.map(msg => {
                                if (msg.msgId === msgId) {
                                    return {
                                        ...msg,
                                        displayContent: htmlContent,
                                        content: lastSuccessfulParse || jsonBuffer,
                                        isStreaming: true
                                    };
                                }
                                return msg;
                            }));
                        }
                    }
                } else {
                    // 使用原始数据显示模式
                    const displayContent = jsonBuffer
                        .replace(/\n/g, '<br>')
                        .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
                        .replace(/  /g, '&nbsp;&nbsp;');

                    setMessageList(prev => prev.map(msg => {
                        if (msg.msgId === msgId) {
                            return {
                                ...msg,
                                displayContent: `<div style="white-space: pre-wrap; font-family: monospace; color: #FFFFFF; font-size: 12px; line-height: 1.4;">${displayContent}</div>`,
                                content: jsonBuffer,
                                isStreaming: true
                            };
                        }
                        return msg;
                    }));
                }

                // 滚动到底部
                scrollToBottom();

                // 检查并更新已完成的任务为折叠形式
                setTimeout(() => {
                    // 从最新解析的数据中获取responseData
                    try {
                        let currentData = lastSuccessfulParse || finalData;
                        if (currentData && Array.isArray(currentData)) {
                            updateCompletedTasksToCollapsible(currentData);
                        }
                    } catch (e) {
                        console.warn('更新折叠任务时出错:', e);
                    }
                }, 100);
            }, 50); // 50ms 节流
        };

        try {
            await api.postVoice(params, (chunk: string) => {
                console.log('Received chunk:', JSON.stringify(chunk)); // 更详细的调试日志
                console.log('Buffer length before:', jsonBuffer.length);

                try {
                    // 累积JSON数据
                    jsonBuffer += chunk;
                    console.log('Buffer length after:', jsonBuffer.length);
                    console.log('Current buffer preview:', jsonBuffer.slice(-100)); // 显示最后100个字符

                    // 使用节流更新
                    throttledUpdate();

                } catch (e) {
                    console.error('Error processing stream chunk:', e);
                }
            });

            // 清理定时器
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }

            // 流式传输完成，保存最终数据
            responseDataRef.current = finalData || jsonBuffer;
            setTaskData(finalData || jsonBuffer);

        } catch (error) {
            console.error('Streaming error:', error);
            // 如果流式传输失败，回退到普通请求
            try {
                const res = await api.postVoice(params);
                const fallbackContent = generatePlanHTML(res);

                setMessageList(prev => prev.map(msg => {
                    if (msg.msgId === msgId) {
                        return {
                            ...msg,
                            displayContent: fallbackContent,
                            content: res
                        };
                    }
                    return msg;
                }));

                responseDataRef.current = res;
                setTaskData(res || {});
            } catch (fallbackError) {
                console.error('Fallback request also failed:', fallbackError);
                setMessageList(prev => prev.map(msg => {
                    if (msg.msgId === msgId) {
                        return {
                            ...msg,
                            displayContent: '<p>请求失败，请重试</p>',
                            content: '请求失败'
                        };
                    }
                    return msg;
                }));
            }
        }
    };

    // 增强的流式生成混合内容函数（使用 jsonrepair）
    const generateStreamingPlanHTML = (jsonBuffer: string): string => {
        try {
            // 尝试解析部分JSON数据
            let partialData: any = null;
            let parseMethod = '';

            // 策略1: 首先尝试解析完整的JSON
            try {
                partialData = JSON.parse(jsonBuffer);
                parseMethod = '完整JSON解析';
            } catch (e) {
                // 策略2: 使用 jsonrepair 进行智能修复
                try {
                    const repaired = jsonrepair(jsonBuffer);
                    if (typeof repaired === 'string') {
                        partialData = JSON.parse(repaired);
                    } else {
                        partialData = repaired;
                    }
                    parseMethod = 'jsonrepair智能修复';
                } catch (repairError) {
                    // 策略3: 使用自定义的部分解析逻辑
                    partialData = tryParsePartialJSON(jsonBuffer);
                    parseMethod = '自定义部分解析';
                }
            }

            if (!partialData) {
                return '<p>正在生成方案...</p>';
            }

            console.log(`JSON解析成功，方法: ${parseMethod}`, partialData);

            // 确保数据是数组格式
            const responseData = Array.isArray(partialData) ? partialData : [partialData];

            // 检查是否有足够的完整字段来进行渲染
            const hasRenderableContent = checkRenderableContent(responseData);
            if (!hasRenderableContent) {
                return '<p>正在生成方案...</p>';
            }

            let fullContent = ``;

            // 首先检查是否为错误消息（只有message字段，没有其他业务字段）
            if (responseData.length > 0 && responseData[0]?.message) {
                const firstItem = responseData[0];
                // 确保这是一个纯错误消息：只有message字段，没有battlefield_analysis或plans字段
                const isErrorMessage = firstItem.message &&
                                     !firstItem.battlefield_analysis &&
                                     !firstItem.plans &&
                                     !firstItem.plan_name &&
                                     Object.keys(firstItem).length <= 2; // 允许message和可能的其他少量字段

                if (isErrorMessage) {
                    const errorMessage = firstItem.message;
                    fullContent += `<div style="padding: 16px; margin: 10px 0; background-color: rgba(255, 77, 79, 0.1); border: 1px solid #ff4d4f; border-radius: 8px;">`;
                    fullContent += `<p style="font-weight: bold; color: #ff4d4f; text-align: center; margin-bottom: 8px;">⚠️ 执行失败</p>`;
                    fullContent += `<p style="color: #ff4d4f; text-align: center; margin: 0;">${errorMessage}</p>`;
                    fullContent += `</div>`;
                    return fullContent;
                }
            }

            // 处理最新的数据结构：{ battlefield_analysis: "", plans: [...] }
            let battlefieldAnalysis = null;
            let planData = [];

            if (responseData.length > 0) {
                const firstItem = responseData[0];

                if (firstItem?.battlefield_analysis && firstItem?.plans && Array.isArray(firstItem.plans)) {
                    // 最新格式：{ battlefield_analysis: "", plans: [...] }
                    battlefieldAnalysis = firstItem.battlefield_analysis;
                    planData = firstItem.plans;
                } else if (firstItem?.battlefield_analysis && !firstItem?.plan_name) {
                    // 中间格式：第一个对象是战场分析，后续对象为方案
                    battlefieldAnalysis = firstItem.battlefield_analysis;
                    planData = responseData.slice(1);
                } else {
                    // 兼容旧格式：第一个对象既有战场分析又有方案信息
                    if (firstItem?.battlefield_analysis) {
                        battlefieldAnalysis = firstItem.battlefield_analysis;
                    }
                    // 过滤出方案数据
                    planData = responseData.filter((item: any) =>
                        item && typeof item === 'object' &&
                        (item.plan_name || item.plan_desc || item.plan_rationality || item.plan_tasks)
                    );
                }
            }

            // 添加战场分析
            if (battlefieldAnalysis) {
                fullContent += `<p style="font-weight: bold; color: #4096ff;text-align: center;">战场分析</p>`;
                fullContent += `<p>${battlefieldAnalysis}</p>`;
            }

            // 分析哪些方案是完整的
            const { completeData } = analyzeDataCompleteness(jsonBuffer, planData);

            // 渲染完整的方案
            completeData.forEach((plan: any, index: number) => {
                if (!plan || typeof plan !== 'object') return;

                fullContent += `<p style="font-weight: bold; color: #4096ff; margin-top: 16px;text-align: center; cursor: pointer; padding: 8px; border-radius: 4px; transition: background-color 0.2s;" data-plan-index="${index}" class="plan-header" onmouseover="this.style.backgroundColor='rgba(64, 150, 255, 0.1)'" onmouseout="this.style.backgroundColor='transparent'">方案 ${index + 1}</p>`;

                // 方案基本信息
                fullContent += `<div>`;

                if (plan.plan_name) {
                    fullContent += `<p style="text-align: center;">${plan.plan_name}</p>`;
                }

                if (plan.plan_desc || plan.plan_rationality) {
                    fullContent += `<p style="text-align: center;"><strong>合理性描述</strong></p>`;
                    if (plan.plan_desc) {
                        fullContent += `<p>${plan.plan_desc}</p>`;
                    }
                    if (plan.plan_rationality) {
                        fullContent += `<p>${plan.plan_rationality}</p>`;
                    }
                }

                // 任务列表 - 字段级流式渲染
                if (plan.plan_tasks?.length) {
                    fullContent += `<p style="font-weight: bold; text-align: center;">详细任务</p>`;

                    plan.plan_tasks.forEach((task: any, taskIndex: number) => {
                        if (!task || typeof task !== 'object') return;

                        // 为每个任务生成唯一ID，用于后续动态更新
                        const taskId = `task-${index}-${taskIndex}`;

                        // 判断当前任务是否完整
                        const isTaskComplete = isTaskDataComplete(task);

                        if (isTaskComplete) {
                            // 任务完整：使用折叠标签（默认关闭）
                            fullContent += `<div id="${taskId}" class="task-container" style="margin: 5px 0;">
                                <details class="task-details">
                                    <summary class="task-summary" style="cursor: pointer; display: flex; align-items: center;">
                                        <span class="arrow" style="display: inline-block; transition: transform 0.2s; margin-right: 8px;"></span>
                                        <strong>${task.task_name || `任务 ${taskIndex + 1}`}</strong>
                                    </summary>
                                    <div style="padding-left: 20px;">`;

                            // 渲染所有可用字段
                            if (task.force_allocation && Array.isArray(task.force_allocation) && task.force_allocation.length > 0) {
                                fullContent += `<p><em>兵力配置：</em>${task.force_allocation.map((f: any) =>
                                    `派遣我方兵力编号:${f.red?.join(',')}，敌方目标编号:${f.blue}`
                                ).join('<br>')}</p>`;
                            }
                            if (task.task_desc && typeof task.task_desc === 'string' && task.task_desc.trim()) {
                                fullContent += `<p><em>描述：</em>${task.task_desc}</p>`;
                            }
                            if (task.task_time && typeof task.task_time === 'string' && task.task_time.trim()) {
                                                            fullContent += `<p><em>预估时段：</em>${task.task_time}</p>`;
                                                        }
                            if (task.formation && typeof task.formation === 'string' && task.formation.trim()) {
                                fullContent += `<p><em>编队：</em>${task.formation}</p>`;
                            }

                            fullContent += `</div>
                                </details>
                            </div>`;
                        } else {
                            // 任务不完整：字段级流式渲染
                            fullContent += `<div id="${taskId}" class="task-container task-streaming" style="margin: 10px 0; padding: 10px; border-left: 3px solid #4096ff; background: rgba(64, 150, 255, 0.05);">`;

                            // 任务标题 - 优先显示
                            if (task.task_name && typeof task.task_name === 'string' && task.task_name.trim()) {
                                fullContent += `<p style="font-weight: bold; color: #4096ff; margin-bottom: 8px;">${task.task_name}</p>`;
                            } else {
                                fullContent += `<p style="font-weight: bold; color: #4096ff; margin-bottom: 8px;">任务 ${taskIndex + 1}</p>`;
                            }

                            // 逐个字段渲染 - 每个字段独立显示
                            if (task.force_allocation && Array.isArray(task.force_allocation) && task.force_allocation.length > 0) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>兵力配置：</em>${task.force_allocation.map((f: any) =>
                                    `派遣我方兵力编号:${f.red?.join(',')}，敌方目标编号:${f.blue}`
                                ).join('<br>')}</p>`;
                            }
                            if (task.task_desc && typeof task.task_desc === 'string' && task.task_desc.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>描述：</em>${task.task_desc}</p>`;
                            }
                            if (task.task_time && typeof task.task_time === 'string' && task.task_time.trim()) {
                                                            fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>预估时段：</em>${task.task_time}</p>`;
                                                        }
                            if (task.formation && typeof task.formation === 'string' && task.formation.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>编队：</em>${task.formation}</p>`;
                            }

                            // 添加其他可能的字段
                            if (task.task_target && typeof task.task_target === 'string' && task.task_target.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>任务目标：</em>${task.task_target}</p>`;
                            }

                            if (task.task_type && typeof task.task_type === 'string' && task.task_type.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>任务类型：</em>${task.task_type}</p>`;
                            }

                            if (task.tactical_target && typeof task.tactical_target === 'string' && task.tactical_target.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0; border-bottom: 1px solid rgba(64, 150, 255, 0.1);"><em>战术目标：</em>${task.tactical_target}</p>`;
                            }

                            if (task.tactical_understand && typeof task.tactical_understand === 'string' && task.tactical_understand.trim()) {
                                fullContent += `<p style="margin: 4px 0; padding: 2px 0;"><em>战术理解：</em>${task.tactical_understand}</p>`;
                            }

                            fullContent += `</div>`;
                        }
                    });
                }
                fullContent += `</div>`;
            });

            fullContent += `<style>
                .task-summary::-webkit-details-marker { display: none; }
                .task-details .arrow::before { content: '▶'; display: inline-block; }
                .task-details[open] .arrow::before { content: '▼'; }
            </style>`;

            return fullContent;

        } catch (error) {
            console.warn('流式解析JSON时出错:', error);
            return '<p>正在解析数据...</p>';
        }
    };

    // 分析数据完整性，对每个字段单独处理
    const analyzeDataCompleteness = (jsonBuffer: string, responseData: any[]): { completeData: any[], incompleteData: string | null } => {
        try {
            // 如果JSON完整，所有数据都是完整的
            JSON.parse(jsonBuffer);
            return { completeData: responseData, incompleteData: null };
        } catch (e) {
            // JSON不完整，对每个方案的每个字段单独分析
            const completeData: any[] = [];

            for (let i = 0; i < responseData.length; i++) {
                const plan = responseData[i];
                if (plan && typeof plan === 'object') {
                    // 创建一个新的方案对象，只包含完整的字段
                    const completePlan: any = {};
                    let hasAnyCompleteField = false;

                    // 检查基本信息字段
                    if (plan.plan_name && typeof plan.plan_name === 'string' && plan.plan_name.trim()) {
                        completePlan.plan_name = plan.plan_name;
                        hasAnyCompleteField = true;
                    }

                    if (plan.plan_desc && typeof plan.plan_desc === 'string' && plan.plan_desc.trim()) {
                        completePlan.plan_desc = plan.plan_desc;
                        hasAnyCompleteField = true;
                    }

                    if (plan.plan_rationality && typeof plan.plan_rationality === 'string' && plan.plan_rationality.trim()) {
                        completePlan.plan_rationality = plan.plan_rationality;
                        hasAnyCompleteField = true;
                    }

                    // 检查任务列表，对每个任务的每个字段进行独立处理
                    if (plan.plan_tasks && Array.isArray(plan.plan_tasks)) {
                        const completeTasks: any[] = [];

                        plan.plan_tasks.forEach((task: any) => {
                            if (task && typeof task === 'object') {
                                const completeTask: any = {};
                                let hasCompleteTaskField = false;

                                // 独立检查每个任务字段，只要有值就保留
                                if (task.task_name && typeof task.task_name === 'string' && task.task_name.trim()) {
                                    completeTask.task_name = task.task_name;
                                    hasCompleteTaskField = true;
                                }

                                if (task.task_desc && typeof task.task_desc === 'string' && task.task_desc.trim()) {
                                    completeTask.task_desc = task.task_desc;
                                    hasCompleteTaskField = true;
                                }

                                if (task.task_time && typeof task.task_time === 'string' && task.task_time.trim()) {
                                    completeTask.task_time = task.task_time;
                                    hasCompleteTaskField = true;
                                }

                                if (task.formation && typeof task.formation === 'string' && task.formation.trim()) {
                                    completeTask.formation = task.formation;
                                    hasCompleteTaskField = true;
                                }

                                if (task.force_allocation && Array.isArray(task.force_allocation) && task.force_allocation.length > 0) {
                                    // 进一步检查兵力配置的完整性
                                    const validAllocations = task.force_allocation.filter((allocation: any) =>
                                        allocation && (allocation.red || allocation.blue)
                                    );
                                    if (validAllocations.length > 0) {
                                        completeTask.force_allocation = validAllocations;
                                        hasCompleteTaskField = true;
                                    }
                                }

                                // 检查其他字段
                                if (task.task_target && typeof task.task_target === 'string' && task.task_target.trim()) {
                                    completeTask.task_target = task.task_target;
                                    hasCompleteTaskField = true;
                                }

                                if (task.task_type && typeof task.task_type === 'string' && task.task_type.trim()) {
                                    completeTask.task_type = task.task_type;
                                    hasCompleteTaskField = true;
                                }

                                if (task.tactical_target && typeof task.tactical_target === 'string' && task.tactical_target.trim()) {
                                    completeTask.tactical_target = task.tactical_target;
                                    hasCompleteTaskField = true;
                                }

                                if (task.tactical_understand && typeof task.tactical_understand === 'string' && task.tactical_understand.trim()) {
                                    completeTask.tactical_understand = task.tactical_understand;
                                    hasCompleteTaskField = true;
                                }

                                // 只要任务有任何一个完整字段，就添加到任务列表
                                // 这样可以实现字段级别的流式显示
                                if (hasCompleteTaskField) {
                                    completeTasks.push(completeTask);
                                }
                            }
                        });

                        // 只要有任何任务（哪怕只有部分字段），就添加任务列表
                        if (completeTasks.length > 0) {
                            completePlan.plan_tasks = completeTasks;
                            hasAnyCompleteField = true;
                        }
                    }

                    // 如果方案有任何完整字段，就添加到完整数据列表
                    if (hasAnyCompleteField) {
                        completeData.push(completePlan);
                    }
                }
            }

            // 不再显示不完整的原始数据
            return { completeData, incompleteData: null };
        }
    };

    // 检查是否有足够的完整字段来进行渲染
    const checkRenderableContent = (responseData: any[]): boolean => {
        if (!responseData || responseData.length === 0) {
            return false;
        }

        // 检查是否有错误消息（只有message字段，没有其他业务字段）
        if (responseData[0]?.message) {
            const firstItem = responseData[0];
            const isErrorMessage = firstItem.message &&
                                 !firstItem.battlefield_analysis &&
                                 !firstItem.plans &&
                                 !firstItem.plan_name &&
                                 Object.keys(firstItem).length <= 2;
            if (isErrorMessage) {
                return true;
            }
        }

        // 检查是否有战场分析
        if (responseData[0]?.battlefield_analysis) {
            return true;
        }

        // 检查是否至少有一个方案包含任何有意义的信息
        for (const plan of responseData) {
            if (plan && typeof plan === 'object') {
                // 只要有任何一个关键字段就可以开始渲染
                if (plan.plan_name || plan.plan_desc || plan.plan_rationality) {
                    return true;
                }
                // 如果有任务列表，也可以渲染
                if (plan.plan_tasks?.length > 0) {
                    return true;
                }
                // 甚至只要有方案的基本结构也可以渲染
                if (Object.keys(plan).length > 0) {
                    return true;
                }
            }
        }

        return false;
    };

    // 检查单个任务数据是否完整（更严格的标准）
    const isTaskDataComplete = (task: any): boolean => {
        if (!task || typeof task !== 'object') return false;

        // 定义任务完整的标准：需要有核心字段
        const hasTaskName = task.task_name && typeof task.task_name === 'string' && task.task_name.trim();
        const hasTaskDesc = task.task_desc && typeof task.task_desc === 'string' && task.task_desc.trim();
        const hasTaskTime = task.task_time && typeof task.task_time === 'string' && task.task_time.trim();
        const hasFormation = task.formation && typeof task.formation === 'string' && task.formation.trim();
        const hasForceAllocation = task.force_allocation && Array.isArray(task.force_allocation) && task.force_allocation.length > 0;

        // 根据您的示例数据，一个完整的任务应该包含：
        // 1. 任务名称 (task_name)
        // 2. 任务描述 (task_desc)
        // 3. 任务时间 (task_time)
        // 4. 编队信息 (formation)
        // 5. 兵力配置 (force_allocation)
        // 6. 可选：任务目标、任务类型、战术目标、战术理解等

        // 基本完整性：至少需要名称、描述、时间、编队和兵力配置
        const hasBasicFields = hasTaskName && hasTaskDesc && hasTaskTime && hasFormation && hasForceAllocation;

        return hasBasicFields;
    };

    // 动态更新已完成的任务为折叠形式
    const updateCompletedTasksToCollapsible = (responseData: any[]) => {
        if (!responseData || !Array.isArray(responseData)) return;

        responseData.forEach((plan: any, planIndex: number) => {
            if (!plan?.plan_tasks?.length) return;

            plan.plan_tasks.forEach((task: any, taskIndex: number) => {
                const taskId = `task-${planIndex}-${taskIndex}`;
                const taskElement = document.getElementById(taskId);

                // 如果任务元素存在且当前是流式状态，检查是否应该转为折叠
                if (taskElement && taskElement.classList.contains('task-streaming')) {
                    const isComplete = isTaskDataComplete(task);

                    if (isComplete) {
                        // 生成折叠版本的HTML
                        const collapsibleHTML = generateCollapsibleTaskHTML(task, taskIndex);

                        // 替换元素内容
                        taskElement.outerHTML = `<div id="${taskId}" class="task-container" style="margin: 5px 0;">${collapsibleHTML}</div>`;
                    }
                }
            });
        });
    };

    // 生成折叠任务的HTML（默认关闭状态）
    const generateCollapsibleTaskHTML = (task: any, taskIndex: number): string => {
        let html = `<details class="task-details">
            <summary class="task-summary" style="cursor: pointer; display: flex; align-items: center;">
                <span class="arrow" style="display: inline-block; transition: transform 0.2s; margin-right: 8px;"></span>
                <strong>${task.task_name || `任务 ${taskIndex + 1}`}</strong>
            </summary>
            <div style="padding-left: 20px;">`;

        // 渲染所有可用字段    
        if (task.force_allocation && Array.isArray(task.force_allocation) && task.force_allocation.length > 0) {
            html += `<p><em>兵力配置：</em>${task.force_allocation.map((f: any) =>
                `派遣我方兵力编号:${f.red?.join(',')}，敌方目标编号:${f.blue}`
            ).join('<br>')}</p>`;
        }
        if (task.task_desc && typeof task.task_desc === 'string' && task.task_desc.trim()) {
            html += `<p><em>描述：</em>${task.task_desc}</p>`;
        }
        if (task.task_time && typeof task.task_time === 'string' && task.task_time.trim()) {
                    html += `<p><em>预估时段：</em>${task.task_time}</p>`;
                }
        if (task.formation && typeof task.formation === 'string' && task.formation.trim()) {
            html += `<p><em>编队：</em>${task.formation}</p>`;
        }


        // 添加其他字段
        if (task.task_target && typeof task.task_target === 'string' && task.task_target.trim()) {
            html += `<p><em>任务目标：</em>${task.task_target}</p>`;
        }

        if (task.task_type && typeof task.task_type === 'string' && task.task_type.trim()) {
            html += `<p><em>任务类型：</em>${task.task_type}</p>`;
        }

        if (task.tactical_target && typeof task.tactical_target === 'string' && task.tactical_target.trim()) {
            html += `<p><em>战术目标：</em>${task.tactical_target}</p>`;
        }

        if (task.tactical_understand && typeof task.tactical_understand === 'string' && task.tactical_understand.trim()) {
            html += `<p><em>战术理解：</em>${task.tactical_understand}</p>`;
        }

        html += `</div></details>`;
        return html;
    };

    // 使用 jsonrepair 库进行智能 JSON 修复
    const tryParsePartialJSON = (jsonStr: string): any => {
        if (!jsonStr.trim()) return null;

        // 移除可能的前后空白字符
        jsonStr = jsonStr.trim();

        // 如果不是以 [ 或 { 开头，返回null
        if (!jsonStr.startsWith('[') && !jsonStr.startsWith('{')) {
            return null;
        }

        console.log('尝试修复 JSON:', jsonStr.slice(0, 200) + (jsonStr.length > 200 ? '...' : ''));

        // 修复策略数组，按优先级排序
        const repairStrategies = [
            // 策略1: 直接使用 jsonrepair 修复
            () => {
                const repaired = jsonrepair(jsonStr);
                if (typeof repaired === 'string') {
                    return JSON.parse(repaired);
                }
                return repaired;
            },

            // 策略2: 先尝试直接解析（可能已经是完整的 JSON）
            () => JSON.parse(jsonStr),

            // 策略3: 手动补全数组结构后使用 jsonrepair
            () => {
                if (jsonStr.startsWith('[') && !jsonStr.endsWith(']')) {
                    // 找到最后一个完整的对象
                    let braceCount = 0;
                    let lastCompleteIndex = -1;

                    for (let i = 0; i < jsonStr.length; i++) {
                        if (jsonStr[i] === '{') braceCount++;
                        else if (jsonStr[i] === '}') {
                            braceCount--;
                            if (braceCount === 0) {
                                lastCompleteIndex = i;
                            }
                        }
                    }

                    if (lastCompleteIndex > -1) {
                        const truncatedJson = jsonStr.substring(0, lastCompleteIndex + 1) + ']';
                        const repaired = jsonrepair(truncatedJson);
                        if (typeof repaired === 'string') {
                            return JSON.parse(repaired);
                        }
                        return repaired;
                    }
                }
                return null;
            },

            // 策略4: 手动补全对象结构后使用 jsonrepair
            () => {
                if (jsonStr.startsWith('{') && !jsonStr.endsWith('}')) {
                    let braceCount = 1;
                    let lastCompleteIndex = -1;

                    for (let i = 1; i < jsonStr.length; i++) {
                        if (jsonStr[i] === '{') braceCount++;
                        else if (jsonStr[i] === '}') {
                            braceCount--;
                            if (braceCount === 0) {
                                lastCompleteIndex = i;
                                break;
                            }
                        }
                    }

                    if (lastCompleteIndex > -1) {
                        const truncatedJson = jsonStr.substring(0, lastCompleteIndex + 1);
                        const repaired = jsonrepair(truncatedJson);
                        if (typeof repaired === 'string') {
                            return JSON.parse(repaired);
                        }
                        return repaired;
                    }
                }
                return null;
            },

            // 策略5: 尝试提取部分有效的 JSON 片段
            () => {
                // 尝试找到第一个完整的对象或数组
                const patterns = [
                    /\{[^{}]*\}/g,  // 简单对象
                    /\[[^\[\]]*\]/g, // 简单数组
                ];

                for (const pattern of patterns) {
                    const matches = jsonStr.match(pattern);
                    if (matches && matches.length > 0) {
                        try {
                            const repaired = jsonrepair(matches[0]);
                            if (typeof repaired === 'string') {
                                return JSON.parse(repaired);
                            }
                            return repaired;
                        } catch (e) {
                            continue;
                        }
                    }
                }
                return null;
            }
        ];

        // 依次尝试各种修复策略
        for (let i = 0; i < repairStrategies.length; i++) {
            try {
                const result = repairStrategies[i]();
                if (result !== null && result !== undefined) {
                    console.log(`JSON 修复成功，使用策略 ${i + 1}:`, result);
                    return result;
                }
            } catch (e) {
                console.warn(`修复策略 ${i + 1} 失败:`, e);
                continue;
            }
        }

        console.warn('所有 JSON 修复策略都失败了');
        return null;
    };

    // 生成方案HTML内容的函数（保留原版本作为备用）
    const generatePlanHTML = (responseData: any[]): string => {
        if (!responseData || responseData.length === 0) {
            return '<p>正在生成方案...</p>';
        }

        let fullContent = ``;

        // 首先检查是否为错误消息（只有message字段，没有其他业务字段）
        if (responseData.length > 0 && responseData[0]?.message) {
            const firstItem = responseData[0];
            // 确保这是一个纯错误消息：只有message字段，没有battlefield_analysis或plans字段
            const isErrorMessage = firstItem.message &&
                                 !firstItem.battlefield_analysis &&
                                 !firstItem.plans &&
                                 !firstItem.plan_name &&
                                 Object.keys(firstItem).length <= 2; // 允许message和可能的其他少量字段

            if (isErrorMessage) {
                const errorMessage = firstItem.message;
                fullContent += `<div style="padding: 16px; margin: 10px 0; background-color: rgba(255, 77, 79, 0.1); border: 1px solid #ff4d4f; border-radius: 8px;">`;
                fullContent += `<p style="font-weight: bold; color: #ff4d4f; text-align: center; margin-bottom: 8px;">⚠️ 执行失败</p>`;
                fullContent += `<p style="color: #ff4d4f; text-align: center; margin: 0;">${errorMessage}</p>`;
                fullContent += `</div>`;
                return fullContent;
            }
        }

        // 处理最新的数据结构：{ battlefield_analysis: "", plans: [...] }
        let battlefieldAnalysis = null;
        let planData = [];

        if (responseData.length > 0) {
            const firstItem = responseData[0];

            if (firstItem?.battlefield_analysis && firstItem?.plans && Array.isArray(firstItem.plans)) {
                // 最新格式：{ battlefield_analysis: "", plans: [...] }
                battlefieldAnalysis = firstItem.battlefield_analysis;
                planData = firstItem.plans;
            } else if (firstItem?.battlefield_analysis && !firstItem?.plan_name) {
                // 中间格式：第一个对象是战场分析，后续对象为方案
                battlefieldAnalysis = firstItem.battlefield_analysis;
                planData = responseData.slice(1);
            } else {
                // 兼容旧格式：第一个对象既有战场分析又有方案信息
                if (firstItem?.battlefield_analysis) {
                    battlefieldAnalysis = firstItem.battlefield_analysis;
                }
                planData = responseData;
            }
        }

        // 添加战场分析
        if (battlefieldAnalysis) {
            fullContent += `<p style="font-weight: bold; color: #4096ff;text-align: center;">战场分析</p>`;
            fullContent += `<p>${battlefieldAnalysis}</p>`;
        }

        // 遍历所有方案
        planData.forEach((plan: any, index: number) => {
            fullContent += `<p style="font-weight: bold; color: #4096ff; margin-top: 16px;text-align: center; cursor: pointer; padding: 8px; border-radius: 4px; transition: background-color 0.2s;" data-plan-index="${index}" class="plan-header" onmouseover="this.style.backgroundColor='rgba(64, 150, 255, 0.1)'" onmouseout="this.style.backgroundColor='transparent'">方案 ${index + 1}</p>`;

            // 方案基本信息
            fullContent += `<div>`;

            if (plan.plan_name) {
                fullContent += `<p style="text-align: center;">${plan.plan_name}</p>`;
            }

            if (plan.plan_desc || plan.plan_rationality) {
                fullContent += `<p style="text-align: center;"><strong>合理性描述</strong></p>`;
                if (plan.plan_desc) {
                    fullContent += `<p>${plan.plan_desc}</p>`;
                }
                if (plan.plan_rationality) {
                    fullContent += `<p>${plan.plan_rationality}</p>`;
                }
            }

            // 任务列表
            if (plan.plan_tasks?.length) {
                fullContent += `<p style="font-weight: bold; text-align: center;">详细任务</p>`;
                fullContent += `<ul style="padding-left: 0;">`;
                plan.plan_tasks.forEach((task: any) => {
                    fullContent += `<li style="list-style:none;">
                        <details class="task-details">
                            <summary class="task-summary" style="cursor: pointer; display: flex; align-items: center;">
                                <span class="arrow" style="display: inline-block; transition: transform 0.2s; margin-right: 8px;"></span>
                                <strong>${task.task_name || ''}</strong>
                            </summary>
                            <div>
                                ${task.force_allocation?.length ?
                            `<p>${task.force_allocation.map((f: any) =>
                                `派遣我方兵力编号:${f.red?.join(',')}，敌方目标编号:${f.blue}`
                            ).join('<br>')}</p>`
                            : ''}
                            <p><em>描述：</em>${task.task_desc || ''}</p>
                            <p><em>预估时段：</em>${task.task_time || ''}</p>
                            <p><em>编队：</em>${task.formation || ''}</p>
                            </div>
                        </details>
                    </li>`;
                });
                fullContent += `</ul>`;
            }
            fullContent += `</div>`;
        });

        fullContent += `<style>
            .task-summary::-webkit-details-marker { display: none; }
            .task-details .arrow::before { content: '▶'; display: inline-block; }
            .task-details[open] .arrow::before { content: '▼'; }
        </style>`;

        return fullContent;
    };



    // 在组件中通过useEffect添加事件监听
    useEffect(() => {
        // 防重复点击的状态
        let isSending = false;
        let lastSendTime = 0;

        const sendPlanData = async (params: any) => {
            const now = Date.now();

            // 防重复点击：1秒内不允许重复发送
            if (isSending || (now - lastSendTime < 1000)) {
                console.log('⚠️ 请勿重复点击，正在发送中或距离上次发送时间过短');
                return;
            }

            isSending = true;
            lastSendTime = now;

            console.log('=== 开始发送方案数据 ===');
            console.log('发送的参数:', params);

            try {
                const res = await api.sendPlanData(params);
                console.log('✅ 方案发送成功:', res);
                // alert('方案发送成功！');
            } catch (err) {
                console.error('❌ 方案发送失败:', err);
                alert('方案发送失败，请重试！');
            } finally {
                isSending = false;
            }
        };

        const handlePlanClick = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            console.log('点击事件触发，目标元素:', target, '类名:', target.className);

            if (target.classList.contains('plan-header')) {
                const planIndex = target.dataset.planIndex;
                console.log('点击了方案标题，索引:', planIndex);

                if (planIndex) {
                    // 直接调用全局函数，保持逻辑一致
                    (window as any).handlePlanClickDirect(Number(planIndex));
                }
            }
        };

        // 添加测试函数到全局
        (window as any).testPlanClick = () => {
            console.log('🧪 测试方案点击功能');
            console.log('📊 当前 responseDataRef:', responseDataRef.current);
            if (responseDataRef.current) {
                (window as any).handlePlanClickDirect(0); // 测试点击第一个方案
            } else {
                console.warn('⚠️ 没有可用的方案数据');
            }
        };

        // 添加全局函数作为备用方案
        (window as any).handlePlanClickDirect = (planIndex: number) => {
            console.log('🔥 直接点击方案，索引:', planIndex);
            const currentData = responseDataRef.current;
            console.log('📊 当前数据类型:', typeof currentData);
            console.log('📊 当前数据:', currentData);

            let selectedPlan = null;

            if (currentData) {
                // 如果 currentData 是字符串，尝试解析
                let parsedData = currentData;
                if (typeof currentData === 'string') {
                    try {
                        parsedData = JSON.parse(currentData);
                        console.log('📝 解析字符串数据成功:', parsedData);
                    } catch (e) {
                        console.error('❌ 解析字符串数据失败:', e);
                        return;
                    }
                }

                // 处理新的数据结构：{ battlefield_analysis: "", plans: [...] }
                if (parsedData && typeof parsedData === 'object') {
                    if (parsedData.battlefield_analysis && parsedData.plans && Array.isArray(parsedData.plans)) {
                        // 直接从根对象的 plans 数组获取方案
                        if (planIndex >= 0 && planIndex < parsedData.plans.length) {
                            selectedPlan = parsedData.plans[planIndex];
                            console.log('✅ 从 plans 数组获取方案 [索引:', planIndex, ']:', selectedPlan);
                        } else {
                            console.warn('⚠️ 方案索引超出范围:', planIndex, '可用方案数量:', parsedData.plans.length);
                        }
                    } else if (Array.isArray(parsedData) && parsedData.length > 0) {
                        // 兼容旧格式：数组格式
                        const firstItem = parsedData[0];

                        if (firstItem?.battlefield_analysis && firstItem?.plans && Array.isArray(firstItem.plans)) {
                            // 数组中第一个对象包含 plans
                            selectedPlan = firstItem.plans[planIndex];
                            console.log('✅ 从数组第一个对象的 plans 获取方案:', selectedPlan);
                        } else if (firstItem?.battlefield_analysis && !firstItem?.plan_name) {
                            // 中间格式：第一个对象是战场分析，后续对象为方案
                            selectedPlan = parsedData[planIndex + 1];
                            console.log('✅ 使用中间格式，获取方案:', selectedPlan);
                        } else {
                            // 兼容旧格式：直接从数组中获取
                            selectedPlan = parsedData[planIndex];
                            console.log('✅ 使用旧格式，获取方案:', selectedPlan);
                        }
                    } else {
                        console.warn('⚠️ 数据格式不符合预期:', parsedData);
                    }
                } else {
                    console.warn('⚠️ 数据不是有效的对象格式');
                }
            } else {
                console.warn('⚠️ 当前数据为空');
            }

            if (selectedPlan) {
                console.log('🚀 准备发送方案数据:', selectedPlan);
                sendPlanData(selectedPlan);
            } else {
                console.warn('❌ 未找到对应的方案数据，索引:', planIndex, '当前数据结构:', currentData);
                alert('未找到对应的方案数据，请重试！');
            }
        };

        // 使用事件委托绑定到聊天容器
        const chatContainer = document.getElementById('chatItems');
        if (chatContainer) {
            chatContainer.addEventListener('click', handlePlanClick);
            console.log('事件监听器已绑定到聊天容器');
            return () => {
                chatContainer.removeEventListener('click', handlePlanClick);
                delete (window as any).handlePlanClickDirect;
                console.log('事件监听器已移除');
            };
        } else {
            // 如果聊天容器不存在，绑定到document
            document.addEventListener('click', handlePlanClick);
            console.log('事件监听器已绑定到document');
            return () => {
                document.removeEventListener('click', handlePlanClick);
                delete (window as any).handlePlanClickDirect;
                console.log('事件监听器已从document移除');
            };
        }
    }, []);

    // 在组件卸载时清理定时器
    useEffect(() => {
        return () => {
            if (streamIntervalRef.current) {
                clearInterval(streamIntervalRef.current);
            }
        };
    }, []);

    const send = async () => {
        if (content) {
            const msList = [...messageList];

            // 用户消息
            msList.push({
                msgId: Math.random() * 10000,
                sendId: '2',
                content: content,
                chatType: 'text',
            });

            // 助手消息
            const assistantMsgId = Math.random() * 10000;
            msList.push({
                msgId: assistantMsgId,
                sendId: '1',
                content: '思考中...',
                chatType: 'text',
                isStreaming: true,
                displayContent: '思考中...',
                // 新增字段
                isEditing: false,
                editContent: '',
            });

            setMessageList(msList);
            setContent('');
            // setIsStreaming(true);
            scrollToBottom();

            try {
                let res: any
                if (modelType == '临机决策') {
                    res = await api.immediateDecision({
                        text: content || '',
                    });

                    // 检查是否为错误响应
                    if (res && res.type === 'error') {
                        // 显示错误信息，使用与作战筹划相同的红色错误样式
                        const errorHtml = `
                            <div style="padding: 16px; margin: 10px 0; background-color: rgba(255, 77, 79, 0.1); border: 1px solid #ff4d4f; border-radius: 8px;">
                                <p style="font-weight: bold; color: #ff4d4f; text-align: center; margin-bottom: 8px;">⚠️ 临机决策执行失败</p>
                                <p style="color: #ff4d4f; text-align: center; margin: 0;">${res.message || '未知错误'}</p>
                            </div>
                        `;

                        const updatedList = msList.map(msg => {
                            if (msg.msgId === assistantMsgId) {
                                return {
                                    ...msg,
                                    content: res.message || '执行失败',
                                    isQuestData: false,
                                    isStreaming: false,
                                    displayContent: errorHtml,
                                    isDecisionData: false, // 错误时不显示为决策数据
                                    rawDecisionData: null,
                                    editContent: ''
                                };
                            }
                            return msg;
                        });

                        setMessageList(updatedList);
                        return; // 错误时直接返回，不继续处理
                    }

                    // 正常处理临机决策数据
                    const contentString = formatObjectToString(res);

                    const updatedList = msList.map(msg => {
                        if (msg.msgId === assistantMsgId) {
                            return {
                                ...msg,
                                content: contentString,
                                isQuestData: false,
                                isStreaming: false,
                                displayContent: '',
                                isDecisionData: true,
                                rawDecisionData: res, // 直接使用返回的整个对象
                                editContent: JSON.stringify(res, null, 2)
                            };
                        }
                        return msg;
                    });

                    setMessageList(updatedList);
                    setTaskData(res || {});
                }
                else {
                    // 作战筹划使用真正的流式输出
                    const params = {
                        model_type: modelType,
                        text: content || '',
                    };

                    console.log('Starting streaming for params:', params); // 调试日志

                    // 先更新消息状态为流式输出状态
                    const updatedList = msList.map(msg => {
                        if (msg.msgId === assistantMsgId) {
                            return {
                                ...msg,
                                content: [],
                                isQuestData: true,
                                isStreaming: true,
                                displayContent: '<p>思考中...</p>'
                            };
                        }
                        return msg;
                    });

                    setMessageList(updatedList);

                    // 开始真正的流式输出
                    await handleRealStreaming(assistantMsgId, params);
                }

            } catch (err) {
                const updatedList = msList.map(msg => {
                    if (msg.msgId === assistantMsgId) {
                        return {
                            ...msg,
                            content: '执行失败！',
                            chatType: 'error',
                            isStreaming: false
                        };
                    }
                    return msg;
                });

                setMessageList(updatedList);
                // setIsStreaming(false);
            }

            scrollToBottom();
        }
    };

    const [modelType, setModelType] = useState('Qwen-72b');
    // 新增 useRef 和状态
    const recognitionRef = useRef<any>(null);
    const [audioStatus, setAudioStatus] = useState(false);

    // 语音识别开始函数
    const startRecording = useCallback(() => {
        const SpeechRecognition = (window as any).SpeechRecognition
            || (window as any).webkitSpeechRecognition;

        const recognition = new SpeechRecognition();
        recognition.lang = 'zh-CN';
        recognition.continuous = true; // 持续识别
        recognition.interimResults = false; // 只获取最终结果

        // 清空输入框并开始录音
        setContent('');

        recognition.onresult = (event: any) => {
            const result = event.results[event.results.length - 1][0].transcript;
            setContent(prev => prev + ' ' + result); // 追加识别结果
        };

        recognition.onerror = (event: any) => {
            if (event.error === 'not-allowed') {
                message.error('请允许麦克风权限');
            }
            setAudioStatus(false);
        };

        recognition.onstart = () => setAudioStatus(true);
        recognition.onend = () => {
            setAudioStatus(false);
            recognitionRef.current = null;
        };

        recognition.start();
        recognitionRef.current = recognition;
    }, []);

    // 语音识别停止函数
    const stopRecording = useCallback(() => {
        if (recognitionRef.current) {
            recognitionRef.current.stop();
        }
    }, []);

    // 按钮点击处理
    const onAudioPlay = useCallback(() => {
        if (!audioStatus) {
            startRecording();
        } else {
            stopRecording();
        }
    }, [audioStatus, startRecording, stopRecording]);
    // 每次接口返回数据要重新渲染对话页面
    useEffect(() => {
        getInitData()
        setMessageList(messageList);
    }, [resSuccess]);

    useEffect(() => {
        return () => {
            if (recognitionRef.current) {
                recognitionRef.current.stop();
            }
        };
    }, []);

    // 渲染决策数据的HTML
    const renderDecisionData = (data: any): string => {
        if (!data) return '<p>无决策数据</p>';

        // 直接将数据转换为格式化的JSON字符串
        let formattedJson;
        try {
            formattedJson = JSON.stringify(data, null, 2);
        } catch (e) {
            formattedJson = '数据格式错误';
        }

        // 简单包装成HTML，设置字体颜色为白色
        const html = `
        <style>
        .decision-data pre::-webkit-scrollbar {
            width: 6px;
        }
        .decision-data pre::-webkit-scrollbar-track {
            background: transparent;
        }
        .decision-data pre::-webkit-scrollbar-thumb {
            background-color: #4096ff;
            border-radius: 6px;
        }
        </style>
        <div class="decision-data">
            <pre style="white-space: pre-wrap; word-break: break-word; font-family: monospace; padding: 0 10px; margin: 0; color: #FFFFFF; font-weight: bold;">${formattedJson}</pre>
        </div>`;

        return html;
    };

    // 发送决策到后端
    const handleSendDecision = (decisionData: any) => {
        try {
            // 确保有内容可发送
            if (!decisionData) {
                message.error('无决策数据可发送');
                return;
            }

            console.log('发送决策数据:', decisionData);

            // 直接发送原始决策数据
            api.sendDecision(decisionData)
                .then(() => {
                    message.success('决策已发送');
                })
                .catch(err => {
                    message.error('发送决策失败');
                    console.error(err);
                });
        } catch (error) {
            message.error('处理决策数据失败');
            console.error(error);
        }
    };

    // 处理兵力部署按钮点击
    const handleForceDeploy = async () => {
        try {
            await api.forceDeploy({ type: "接口形式" });
            message.success('兵力部署请求已发送');
        } catch (err) {
            console.error(err);
            message.error('兵力部署请求失败');
        }
    };
    const changeShape = () => {
        setShape(!shape)
    };

    return (
        <div className='chat-dom'>
            <div id='chatItems' className='chat-content' ref={messagesEnd}>
                {messageList.map((item, index) => {
                    return (
                        <div className={`chat-item chat-item-${item.sendId}`} key={item.msgId || index}>
                            <div className='chat-receiver'>
                                {item?.sendId && item?.sendId === '1' ? (
                                    <>
                                        <div className='avatar' style={{ backgroundColor: '#4096ff' }}>
                                            助手
                                        </div>
                                        {item.chatType === 'toolptip' ? (
                                            shape ? (
                                                <div className='receiver-content helper-content'>
                                                    <p>您好，我是军事方案生成助手。</p>
                                                    <>
                                                        {isEditing ? (
                                                            <>
                                                                <div className="form-item">
                                                                    <span className="input-label">指挥中心经度：</span>
                                                                    <input
                                                                        placeholder='请输入指挥中心经度'
                                                                        value={tempData?.lat || ""}
                                                                        onChange={(e) => handleChange('lat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">指挥中心纬度：</span>
                                                                    <input
                                                                        placeholder='请输入指挥中心纬度'
                                                                        value={tempData?.lon || ""}
                                                                        onChange={(e) => handleChange('lon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">打击区域半径：</span>
                                                                    <input
                                                                        placeholder='请输入打击区域半径'
                                                                        value={tempData?.strike_zone_radius || ""}
                                                                        onChange={(e) => handleChange('strike_zone_radius', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">跟踪区域半径：</span>
                                                                    <input
                                                                        placeholder='请输入跟踪区域半径'
                                                                        value={tempData?.tracking_zone_radius || ""}
                                                                        onChange={(e) => handleChange('tracking_zone_radius', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">监控区域半径：</span>
                                                                    <input
                                                                        placeholder='请输入监控区域半径'
                                                                        value={tempData?.surveillance_zone_radius || ""}
                                                                        onChange={(e) => handleChange('surveillance_zone_radius', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-buttons">
                                                                    <button onClick={handleSave}>保存</button>
                                                                    <button onClick={handleCancel}>取消</button>
                                                                </div>
                                                            </>

                                                        ) : (
                                                            <>
                                                                <p style={{ fontWeight: 'bold', textAlign: 'center' }}>圆形战场区域配置</p>
                                                                <p>当前指挥中心：{initData?.control_center?.lat},{initData?.control_center?.lon}</p>                                                          
                                                                <p>当前打击区域半径：{initData?.strike_zone_radius}  km</p>
                                                                <p>当前跟踪区域半径：{initData?.tracking_zone_radius}  km</p>
                                                                <p>当前监控区域半径：{initData?.surveillance_zone_radius}   km</p>
                                                                <div className="form-buttons">
                                                                    <button onClick={() => handleEdit(initData)}>编辑</button>
                                                                    <button onClick={changeShape}>战场配置切换</button>
                                                                    <button onClick={handleForceDeploy}>区域搜索</button>
                                                                </div>
                                                            </>
                                                        )}
                                                    </>
                                                </div>) : (
                                                <div className='receiver-content helper-content'>
                                                    <p>您好，我是军事方案生成助手。</p>
                                                    <p style={{ fontWeight: 'bold', textAlign: 'center' }}>方形战场区域配置</p>
                                                    <>
                                                        {isRectangleEditing ? (
                                                            <>
                                                            
                                                                <div className="form-item">
                                                                    
                                                                    <span className="input-label">巡逻区域最小纬度：</span>
                                                                    <input
                                                                        placeholder='请输入巡逻区域最小纬度'
                                                                        value={tempRectangleData?.surveillance_minlat || ""}
                                                                        onChange={(e) => handleChange('surveillance_minlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">巡逻区域最大纬度：</span>
                                                                    <input
                                                                        placeholder='请输入巡逻区域最大纬度'
                                                                        value={tempRectangleData?.surveillance_maxlat || ""}
                                                                        onChange={(e) => handleChange('surveillance_maxlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">巡逻区域最小经度：</span>
                                                                    <input
                                                                        placeholder='请输入巡逻区域最小经度'
                                                                        value={tempRectangleData?.surveillance_minlon || ""}
                                                                        onChange={(e) => handleChange('surveillance_minlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">巡逻区域最大经度：</span>
                                                                    <input
                                                                        placeholder='请输入巡逻区域最大经度'
                                                                        value={tempRectangleData?.surveillance_maxlon || ""}
                                                                        onChange={(e) => handleChange('surveillance_maxlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div></div>
                                                                <div className="form-item">
                                                                    <span className="input-label">跟踪区域最小纬度：</span>
                                                                    <input
                                                                        placeholder='请输入跟踪区域最小纬度'
                                                                        value={tempRectangleData?.tracking_minlat || ""}
                                                                        onChange={(e) => handleChange('tracking_minlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">跟踪区域最大纬度：</span>
                                                                    <input
                                                                        placeholder='请输入跟踪区域最大纬度'
                                                                        value={tempRectangleData?.tracking_maxlat || ""}
                                                                        onChange={(e) => handleChange('tracking_maxlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">跟踪区域最小经度：</span>
                                                                    <input
                                                                        placeholder='请输入跟踪区域最小经度'
                                                                        value={tempRectangleData?.tracking_minlon || ""}
                                                                        onChange={(e) => handleChange('tracking_minlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">跟踪区域最大经度：</span>
                                                                    <input
                                                                        placeholder='请输入跟踪区域最大经度'
                                                                        value={tempRectangleData?.tracking_maxlon || ""}
                                                                        onChange={(e) => handleChange('tracking_maxlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div></div>
                                                                <div className="form-item">
                                                                    <span className="input-label">打击区域最小纬度：</span>
                                                                    <input
                                                                        placeholder='请输入打击区域最小纬度'
                                                                        value={tempRectangleData?.strike_minlat || ""}
                                                                        onChange={(e) => handleChange('strike_minlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">打击区域最大纬度：</span>
                                                                    <input
                                                                        placeholder='请输入打击区域最大纬度'
                                                                        value={tempRectangleData?.strike_maxlat || ""}
                                                                        onChange={(e) => handleChange('strike_maxlat', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">打击区域最小经度：</span>
                                                                    <input
                                                                        placeholder='请输入打击区域最小经度'
                                                                        value={tempRectangleData?.strike_minlon || ""}
                                                                        onChange={(e) => handleChange('strike_minlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-item">
                                                                    <span className="input-label">打击区域最大经度：</span>
                                                                    <input
                                                                        placeholder='请输入打击区域最大经度'
                                                                        value={tempRectangleData?.strike_maxlon || ""}
                                                                        onChange={(e) => handleChange('strike_maxlon', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="form-buttons">
                                                                    <button onClick={handleSave}>保存</button>
                                                                    <button onClick={handleCancel}>取消</button>
                                                                </div>
                                                            </>

                                                        ) : (
                                                            <>
                                                                <p>巡逻区域</p>
                                                                <p>最小纬度：{initRectangleData?.surveillance?.minlat}</p>
                                                                <p>最大纬度：{initRectangleData?.surveillance?.maxlat}</p>
                                                                <p>最小经度：{initRectangleData?.surveillance?.minlon}</p>
                                                                <p>最大经度：{initRectangleData?.surveillance?.maxlon}</p>
                                                                <p></p>
                                                                <p>跟踪区域</p>
                                                                <p>最小纬度：{initRectangleData?.tracking?.minlat}</p>
                                                                <p>最大纬度：{initRectangleData?.tracking?.maxlat}</p>
                                                                <p>最小经度：{initRectangleData?.tracking?.minlon}</p>
                                                                <p>最大经度：{initRectangleData?.tracking?.maxlon}</p>
                                                                <p></p>
                                                                <p>打击区域</p>
                                                                <p>最小纬度：{initRectangleData?.strike?.minlat}</p>
                                                                <p>最大纬度：{initRectangleData?.strike?.maxlat}</p>
                                                                <p>最小经度：{initRectangleData?.strike?.minlon}</p>
                                                                <p>最大经度：{initRectangleData?.strike?.maxlon}</p>
                                                                <div className="form-buttons">
                                                                    <button onClick={() => handleEdit(initRectangleData)}>编辑</button>  
                                                                    <button onClick={changeShape}>修改配置</button>
                                                                    <button onClick={handleForceDeploy}>区域检测</button>
                                                                </div>
                                                            </>
                                                        )}
                                                    </>
                                                </div>
                                            )
                                        ) : (
                                            <div
                                                style={{ color: colorObj[item.chatType] || '#FFFFFF', padding: item.isDecisionData ? '0 12px' : '12px 12px' }}
                                                className='receiver-content helper-content'
                                            >
                                                {item.isQuestData ? (
                                                    <div className='task-content'>
                                                        <div
                                                            className="streaming-content"
                                                            style={{ whiteSpace: 'pre-line' }}
                                                            dangerouslySetInnerHTML={{ __html: item.displayContent || '思考中...' }}
                                                        />
                                                    </div>
                                                ) : item.isDecisionData ? (
                                                    <div className='decision-container' style={{ width: '100%' }}>
                                                        {item.isEditing ? (
                                                            <div className="decision-edit-container" style={{ width: '100%', minWidth: '280px' }}>
                                                                <div className="decision-edit-header">
                                                                    <h3 style={{ color: '#FFFFFF', fontWeight: 'bold', textAlign: 'center' }}>编辑决策内容</h3>
                                                                </div>
                                                                <style>
                                                                    {`
                                                                    .decision-edit-textarea::-webkit-scrollbar {
                                                                        width: 6px;
                                                                    }
                                                                    .decision-edit-textarea::-webkit-scrollbar-track {
                                                                        background: transparent;
                                                                    }
                                                                    .decision-edit-textarea::-webkit-scrollbar-thumb {
                                                                        background-color: #4096ff;
                                                                        border-radius: 6px;
                                                                    }
                                                                    `}
                                                                </style>
                                                                <textarea
                                                                    className="decision-edit-textarea"
                                                                    value={item.editContent}
                                                                    onChange={(e) => handleContentEdit(e, index)}
                                                                    style={{
                                                                        width: '100%',
                                                                        minHeight: '400px',
                                                                        padding: '10px',
                                                                        border: 'none',
                                                                        borderRadius: '4px',
                                                                        color: '#FFFFFF',
                                                                        fontSize: '14px',
                                                                        lineHeight: '1.5',
                                                                        fontFamily: 'monospace',
                                                                        background: 'transparent',
                                                                        fontWeight: 'bold',
                                                                        overflowY: 'auto',
                                                                        scrollbarWidth: 'thin',
                                                                        scrollbarColor: '#4096ff transparent'
                                                                    }}
                                                                />
                                                                <div className="decision-edit-actions" style={{
                                                                    marginTop: '10px',
                                                                    display: 'flex',
                                                                    justifyContent: 'flex-end',
                                                                    gap: '10px'
                                                                }}>
                                                                    <Button
                                                                        type="primary"
                                                                        onClick={() => handleSaveDecisionEdit(index)}
                                                                    >
                                                                        保存
                                                                    </Button>
                                                                    <Button
                                                                        onClick={() => handleCancelEdit(index)}
                                                                    >
                                                                        取消
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="decision-content" style={{ width: '100%', borderRadius: '4px', marginTop: '-10px' }}>
                                                                <div className="decision-display" dangerouslySetInnerHTML={{ __html: renderDecisionData(item.rawDecisionData) }} />
                                                                <div className="decision-actions" style={{
                                                                    marginTop: '5px',
                                                                    display: 'flex',
                                                                    justifyContent: 'space-evenly',
                                                                    gap: '8px'
                                                                }}>
                                                                    <Button
                                                                        type="primary"
                                                                        onClick={() => handleSendDecision(item.rawDecisionData)}
                                                                    >
                                                                        发送决策
                                                                    </Button>
                                                                    
                                                                    <Button
                                                                        onClick={() => handleEditContent(index)}
                                                                    >
                                                                        修改决策
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    // 检查是否有 displayContent（用于错误消息等HTML内容）
                                                    item.displayContent ? (
                                                        <div dangerouslySetInnerHTML={{ __html: item.displayContent }} />
                                                    ) : (
                                                        item.content
                                                    )
                                                )}
                                            </div>
                                        )}
                                    </>
                                ) : (
                                    <>
                                        <div style={{ color: '#FFFFFF' }} className='receiver-content user-content'>
                                            {item.content}
                                        </div>
                                        <div className='avatar' style={{ backgroundColor: '#faad14' }}>
                                            用户
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
            <div className='bottom'>
                <Input
                    placeholder='请输入...'
                    value={content}
                    onChange={e => {
                        setContent(e.target.value.replace(/\\n/g, ''));
                    }}
                    onPressEnter={e => {
                        e.preventDefault();
                        send();
                    }}
                />
                <Select
                    defaultValue="Qwen-72b"
                    style={{ width: 120, paddingLeft: 3 }}
                    onChange={(value) => {
                        setModelType(value)
                    }}
                    options={[
                        { value: 'Qwen-72b', label: '作战筹划' },
                        { value: '临机决策', label: '临机决策' },
                        // { value: '任务终止', label: '任务终止' }
                    ]}
                />
                <Tooltip title={audioStatus ? "停止录音" : "开始录音"}>
                    {audioStatus ? (
                        <PauseCircleFilled
                            className='audio-icon pause'
                            onClick={onAudioPlay}
                        />
                    ) : (
                        <AudioFilled
                            className='audio-icon'
                            onClick={onAudioPlay}
                        />
                    )}
                </Tooltip>
                <div className='bottom_right'>
                    <button
                        className='send'
                        onClick={e => {
                            e.preventDefault();
                            send();
                        }}
                    >
                        发送
                    </button>
                </div>
            </div>
            {
                spinning && (
                    <div className='all-spinning'>
                        <Spin tip='Loading...' spinning={spinning} size='large' />
                    </div>
                )
            }
        </div >
    );
};

export default Chat;